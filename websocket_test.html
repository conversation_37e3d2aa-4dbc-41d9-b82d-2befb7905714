<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Booking API - WebSocket Real-time Dashboard Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .control-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: background-color 0.2s;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background: white;
        }
        .message.sent {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .message.received {
            border-left-color: #007bff;
            background: #f8f9ff;
        }
        .message.error {
            border-left-color: #dc3545;
            background: #fff8f8;
        }
        .timestamp {
            color: #6c757d;
            font-size: 10px;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .metric-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
        }
        input, select {
            width: 100%;
            padding: 6px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Flight Booking API - WebSocket Real-time Dashboard Test</h1>
            <p>Test the real-time monitoring WebSocket endpoint with live metrics and alerts</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <h3>🔌 Connection</h3>
                <input type="text" id="wsUrl" value="ws://localhost:8000/apis/advanced/dashboard/realtime" placeholder="WebSocket URL">
                <button onclick="connect()" id="connectBtn">Connect</button>
                <button onclick="disconnect()" id="disconnectBtn" disabled>Disconnect</button>
                <div id="connectionStatus" class="status disconnected">Disconnected</div>
            </div>

            <div class="control-group">
                <h3>📊 Subscriptions</h3>
                <select id="subscriptionType">
                    <option value="system_metrics">System Metrics</option>
                    <option value="performance_alerts">Performance Alerts</option>
                    <option value="cache_statistics">Cache Statistics</option>
                    <option value="database_metrics">Database Metrics</option>
                    <option value="booking_analytics">Booking Analytics</option>
                </select>
                <button onclick="subscribe()" id="subscribeBtn" disabled>Subscribe</button>
                <button onclick="unsubscribe()" id="unsubscribeBtn" disabled>Unsubscribe</button>
            </div>

            <div class="control-group">
                <h3>💬 Messages</h3>
                <button onclick="sendPing()" id="pingBtn" disabled>Send Ping</button>
                <button onclick="getHistoricalData()" id="historyBtn" disabled>Get Historical Data</button>
                <button onclick="clearMessages()">Clear Messages</button>
            </div>

            <div class="control-group">
                <h3>📈 Statistics</h3>
                <div>Messages Sent: <span id="sentCount">0</span></div>
                <div>Messages Received: <span id="receivedCount">0</span></div>
                <div>Connection Time: <span id="connectionTime">-</span></div>
                <div>Last Activity: <span id="lastActivity">-</span></div>
            </div>
        </div>

        <div>
            <h3>📝 Message Log</h3>
            <div id="messages" class="messages"></div>
        </div>

        <div class="metrics">
            <div class="metric-card">
                <h4>🎯 Response Time</h4>
                <div class="metric-value" id="responseTime">-</div>
                <div class="metric-label">milliseconds</div>
            </div>
            <div class="metric-card">
                <h4>📊 Cache Hit Rate</h4>
                <div class="metric-value" id="cacheHitRate">-</div>
                <div class="metric-label">percentage</div>
            </div>
            <div class="metric-card">
                <h4>🔄 Request Count</h4>
                <div class="metric-value" id="requestCount">-</div>
                <div class="metric-label">total requests</div>
            </div>
            <div class="metric-card">
                <h4>⚡ Performance Score</h4>
                <div class="metric-value" id="performanceScore">-</div>
                <div class="metric-label">out of 100</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let sentCount = 0;
        let receivedCount = 0;
        let connectionStartTime = null;
        let currentSubscriptions = new Set();

        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
            
            // Update button states
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const subscribeBtn = document.getElementById('subscribeBtn');
            const unsubscribeBtn = document.getElementById('unsubscribeBtn');
            const pingBtn = document.getElementById('pingBtn');
            const historyBtn = document.getElementById('historyBtn');
            
            if (status === 'connected') {
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                subscribeBtn.disabled = false;
                unsubscribeBtn.disabled = false;
                pingBtn.disabled = false;
                historyBtn.disabled = false;
            } else {
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                subscribeBtn.disabled = true;
                unsubscribeBtn.disabled = true;
                pingBtn.disabled = true;
                historyBtn.disabled = true;
            }
        }

        function addMessage(type, content, timestamp = new Date()) {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timeStr = timestamp.toLocaleTimeString();
            messageEl.innerHTML = `
                <div class="timestamp">${timeStr}</div>
                <pre>${JSON.stringify(content, null, 2)}</pre>
            `;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
            
            // Update activity time
            document.getElementById('lastActivity').textContent = timeStr;
        }

        function updateMetrics(data) {
            if (data.metric) {
                const metric = data.metric;
                
                if (metric.metric_type === 'response_time') {
                    document.getElementById('responseTime').textContent = metric.value.toFixed(1);
                }
                
                if (metric.metadata && metric.metadata.cache_hit_rate) {
                    document.getElementById('cacheHitRate').textContent = metric.metadata.cache_hit_rate.toFixed(1) + '%';
                }
                
                if (metric.metadata && metric.metadata.request_count) {
                    document.getElementById('requestCount').textContent = metric.metadata.request_count.toLocaleString();
                }
                
                if (metric.metadata && metric.metadata.performance_score) {
                    document.getElementById('performanceScore').textContent = metric.metadata.performance_score;
                }
            }
        }

        function updateStats() {
            document.getElementById('sentCount').textContent = sentCount;
            document.getElementById('receivedCount').textContent = receivedCount;
            
            if (connectionStartTime) {
                const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('connectionTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            updateConnectionStatus('connecting', 'Connecting...');
            addMessage('sent', { action: 'connecting', url: url });
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    connectionStartTime = Date.now();
                    updateConnectionStatus('connected', 'Connected');
                    addMessage('received', { 
                        type: 'connection_established',
                        message: 'WebSocket connection established successfully',
                        available_subscriptions: [
                            'system_metrics',
                            'performance_alerts', 
                            'cache_statistics',
                            'database_metrics',
                            'booking_analytics'
                        ]
                    });
                };
                
                ws.onmessage = function(event) {
                    receivedCount++;
                    const data = JSON.parse(event.data);
                    addMessage('received', data);
                    updateMetrics(data);
                    updateStats();
                };
                
                ws.onclose = function(event) {
                    updateConnectionStatus('disconnected', 'Disconnected');
                    addMessage('error', { 
                        type: 'connection_closed',
                        code: event.code,
                        reason: event.reason || 'Connection closed'
                    });
                    connectionStartTime = null;
                    currentSubscriptions.clear();
                };
                
                ws.onerror = function(error) {
                    updateConnectionStatus('disconnected', 'Connection Error');
                    addMessage('error', { 
                        type: 'connection_error',
                        message: 'WebSocket connection error occurred'
                    });
                };
                
            } catch (error) {
                updateConnectionStatus('disconnected', 'Connection Failed');
                addMessage('error', { 
                    type: 'connection_failed',
                    message: error.message
                });
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function subscribe() {
            const subscriptionType = document.getElementById('subscriptionType').value;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'subscribe',
                    subscription: subscriptionType
                };
                
                ws.send(JSON.stringify(message));
                sentCount++;
                currentSubscriptions.add(subscriptionType);
                addMessage('sent', message);
                updateStats();
            }
        }

        function unsubscribe() {
            const subscriptionType = document.getElementById('subscriptionType').value;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'unsubscribe',
                    subscription: subscriptionType
                };
                
                ws.send(JSON.stringify(message));
                sentCount++;
                currentSubscriptions.delete(subscriptionType);
                addMessage('sent', message);
                updateStats();
            }
        }

        function sendPing() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = { type: 'ping' };
                ws.send(JSON.stringify(message));
                sentCount++;
                addMessage('sent', message);
                updateStats();
            }
        }

        function getHistoricalData() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'get_historical_data',
                    data_type: 'performance_report',
                    hours: 1
                };
                
                ws.send(JSON.stringify(message));
                sentCount++;
                addMessage('sent', message);
                updateStats();
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            sentCount = 0;
            receivedCount = 0;
            updateStats();
        }

        // Update connection time every second
        setInterval(updateStats, 1000);

        // Auto-connect on page load
        window.addEventListener('load', function() {
            // Uncomment the next line to auto-connect on page load
            // connect();
        });
    </script>
</body>
</html>
