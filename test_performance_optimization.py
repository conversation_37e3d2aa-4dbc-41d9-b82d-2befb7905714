#!/usr/bin/env python3
"""
Performance optimization test script for flight booking API.
Tests the optimized endpoints and validates performance improvements.
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any, List
from datetime import datetime, timedelta


class PerformanceTestSuite:
    """Test suite for validating performance optimizations."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def test_optimized_search_performance(self) -> Dict[str, Any]:
        """Test optimized search endpoint performance."""
        print("🚀 Testing Optimized Search Performance...")
        
        # Test data
        search_request = {
            "Trips": [
                {
                    "From": "DEL",
                    "To": "BOM", 
                    "OnwardDate": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
                }
            ],
            "ADT": 1,
            "CHD": 0,
            "INF": 0,
            "Cabin": "E",
            "FareType": "REGULAR"
        }
        
        results = []
        
        # Test 1: First request (cache miss expected)
        print("  📊 Test 1: First request (cache miss)...")
        start_time = time.time()
        
        async with self.session.post(
            f"{self.base_url}/optimized/search/optimized",
            json=search_request,
            headers={"X-Request-ID": "test-001"}
        ) as response:
            response_data = await response.json()
            response_time = (time.time() - start_time) * 1000
            
            results.append({
                "test": "first_request",
                "response_time_ms": response_time,
                "status_code": response.status,
                "cache_hit": response.headers.get("X-Cache-Hit", "false") == "true",
                "data_source": response.headers.get("X-Data-Source", "unknown"),
                "target_met": response_time <= 3000
            })
            
            print(f"    ⏱️  Response time: {response_time:.2f}ms")
            print(f"    🎯 Target met: {'✅' if response_time <= 3000 else '❌'}")
            print(f"    💾 Cache hit: {response.headers.get('X-Cache-Hit', 'false')}")
        
        # Test 2: Immediate second request (cache hit expected)
        print("  📊 Test 2: Immediate second request (cache hit)...")
        start_time = time.time()
        
        async with self.session.post(
            f"{self.base_url}/optimized/search/optimized",
            json=search_request,
            headers={"X-Request-ID": "test-002"}
        ) as response:
            response_data = await response.json()
            response_time = (time.time() - start_time) * 1000
            
            results.append({
                "test": "cache_hit_request",
                "response_time_ms": response_time,
                "status_code": response.status,
                "cache_hit": response.headers.get("X-Cache-Hit", "false") == "true",
                "data_source": response.headers.get("X-Data-Source", "unknown"),
                "target_met": response_time <= 1000  # Stricter target for cache hits
            })
            
            print(f"    ⏱️  Response time: {response_time:.2f}ms")
            print(f"    🎯 Cache target met: {'✅' if response_time <= 1000 else '❌'}")
            print(f"    💾 Cache hit: {response.headers.get('X-Cache-Hit', 'false')}")
        
        # Test 3: Concurrent requests (deduplication test)
        print("  📊 Test 3: Concurrent requests (deduplication)...")
        concurrent_tasks = []
        
        for i in range(5):
            task = self.make_concurrent_request(search_request, f"test-concurrent-{i}")
            concurrent_tasks.append(task)
        
        concurrent_results = await asyncio.gather(*concurrent_tasks)
        
        # Analyze concurrent results
        response_times = [r["response_time_ms"] for r in concurrent_results]
        cache_hits = sum(1 for r in concurrent_results if r["cache_hit"])
        
        results.append({
            "test": "concurrent_requests",
            "total_requests": len(concurrent_results),
            "avg_response_time_ms": sum(response_times) / len(response_times),
            "max_response_time_ms": max(response_times),
            "min_response_time_ms": min(response_times),
            "cache_hits": cache_hits,
            "deduplication_rate": cache_hits / len(concurrent_results),
            "all_targets_met": all(r["target_met"] for r in concurrent_results)
        })
        
        print(f"    ⏱️  Avg response time: {results[-1]['avg_response_time_ms']:.2f}ms")
        print(f"    🔄 Deduplication rate: {results[-1]['deduplication_rate']:.1%}")
        print(f"    🎯 All targets met: {'✅' if results[-1]['all_targets_met'] else '❌'}")
        
        return {
            "test_name": "optimized_search_performance",
            "timestamp": datetime.now().isoformat(),
            "results": results,
            "summary": {
                "total_tests": len(results),
                "performance_targets_met": sum(1 for r in results if r.get("target_met", r.get("all_targets_met", False))),
                "cache_effectiveness": cache_hits > 0,
                "deduplication_working": results[-1]["deduplication_rate"] > 0.2
            }
        }
    
    async def make_concurrent_request(self, search_request: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """Make a concurrent request for deduplication testing."""
        start_time = time.time()
        
        async with self.session.post(
            f"{self.base_url}/optimized/search/optimized",
            json=search_request,
            headers={"X-Request-ID": request_id}
        ) as response:
            response_time = (time.time() - start_time) * 1000
            
            return {
                "request_id": request_id,
                "response_time_ms": response_time,
                "status_code": response.status,
                "cache_hit": response.headers.get("X-Cache-Hit", "false") == "true",
                "data_source": response.headers.get("X-Data-Source", "unknown"),
                "target_met": response_time <= 3000
            }
    
    async def test_performance_stats_endpoint(self) -> Dict[str, Any]:
        """Test the performance stats endpoint."""
        print("📊 Testing Performance Stats Endpoint...")
        
        start_time = time.time()
        
        async with self.session.get(
            f"{self.base_url}/optimized/search/performance/stats"
        ) as response:
            response_time = (time.time() - start_time) * 1000
            
            if response.status == 200:
                stats_data = await response.json()
                
                print(f"  ⏱️  Stats response time: {response_time:.2f}ms")
                print(f"  📈 Search performance available: {'✅' if 'search_performance' in stats_data else '❌'}")
                print(f"  💾 Cache performance available: {'✅' if 'cache_performance' in stats_data else '❌'}")
                print(f"  🔧 System analytics available: {'✅' if 'system_analytics' in stats_data else '❌'}")
                
                return {
                    "test_name": "performance_stats",
                    "response_time_ms": response_time,
                    "status_code": response.status,
                    "data_available": {
                        "search_performance": "search_performance" in stats_data,
                        "cache_performance": "cache_performance" in stats_data,
                        "system_analytics": "system_analytics" in stats_data
                    },
                    "stats_sample": {
                        k: v for k, v in stats_data.items() 
                        if k in ["timestamp", "performance_targets"]
                    }
                }
            else:
                print(f"  ❌ Stats endpoint failed: {response.status}")
                return {
                    "test_name": "performance_stats",
                    "response_time_ms": response_time,
                    "status_code": response.status,
                    "error": "Endpoint not available"
                }
    
    async def test_cache_warming(self) -> Dict[str, Any]:
        """Test cache warming functionality."""
        print("🔥 Testing Cache Warming...")
        
        # Test different routes to trigger cache warming
        test_routes = [
            {"From": "DEL", "To": "BOM"},
            {"From": "BOM", "To": "DEL"},
            {"From": "DEL", "To": "BLR"},
        ]
        
        results = []
        
        for i, route in enumerate(test_routes):
            search_request = {
                "Trips": [
                    {
                        **route,
                        "OnwardDate": (datetime.now() + timedelta(days=30 + i)).strftime("%Y-%m-%d")
                    }
                ],
                "ADT": 1,
                "CHD": 0,
                "INF": 0,
                "Cabin": "E",
                "FareType": "REGULAR"
            }
            
            start_time = time.time()
            
            async with self.session.post(
                f"{self.base_url}/optimized/search/optimized",
                json=search_request,
                headers={"X-Request-ID": f"cache-warm-{i}"}
            ) as response:
                response_time = (time.time() - start_time) * 1000
                
                results.append({
                    "route": f"{route['From']}-{route['To']}",
                    "response_time_ms": response_time,
                    "status_code": response.status,
                    "cache_hit": response.headers.get("X-Cache-Hit", "false") == "true",
                    "target_met": response_time <= 3000
                })
                
                print(f"  🛫 Route {route['From']}-{route['To']}: {response_time:.2f}ms")
        
        return {
            "test_name": "cache_warming",
            "results": results,
            "summary": {
                "routes_tested": len(results),
                "avg_response_time": sum(r["response_time_ms"] for r in results) / len(results),
                "targets_met": sum(1 for r in results if r["target_met"])
            }
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests."""
        print("🧪 Starting Performance Optimization Test Suite")
        print("=" * 60)
        
        all_results = {}
        
        try:
            # Test 1: Optimized search performance
            all_results["search_performance"] = await self.test_optimized_search_performance()
            print()
            
            # Test 2: Performance stats endpoint
            all_results["stats_endpoint"] = await self.test_performance_stats_endpoint()
            print()
            
            # Test 3: Cache warming
            all_results["cache_warming"] = await self.test_cache_warming()
            print()
            
            # Generate summary
            all_results["test_summary"] = self.generate_test_summary(all_results)
            
        except Exception as e:
            print(f"❌ Test suite failed: {str(e)}")
            all_results["error"] = str(e)
        
        return all_results
    
    def generate_test_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of all test results."""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_tests_run": len([k for k in results.keys() if k != "test_summary"]),
            "performance_optimization_status": "✅ PASSED",
            "key_metrics": {},
            "recommendations": []
        }
        
        # Analyze search performance
        if "search_performance" in results:
            search_results = results["search_performance"]["results"]
            cache_hit_test = next((r for r in search_results if r["test"] == "cache_hit_request"), None)
            concurrent_test = next((r for r in search_results if r["test"] == "concurrent_requests"), None)
            
            if cache_hit_test:
                summary["key_metrics"]["cache_hit_response_time"] = cache_hit_test["response_time_ms"]
            
            if concurrent_test:
                summary["key_metrics"]["deduplication_rate"] = concurrent_test["deduplication_rate"]
                summary["key_metrics"]["concurrent_avg_response"] = concurrent_test["avg_response_time_ms"]
        
        # Analyze cache warming
        if "cache_warming" in results:
            warming_summary = results["cache_warming"]["summary"]
            summary["key_metrics"]["cache_warming_avg_response"] = warming_summary["avg_response_time"]
        
        # Generate recommendations
        if summary["key_metrics"].get("cache_hit_response_time", 0) > 500:
            summary["recommendations"].append("Consider increasing memory cache size for better performance")
        
        if summary["key_metrics"].get("deduplication_rate", 0) < 0.3:
            summary["recommendations"].append("Review deduplication configuration for better efficiency")
        
        if not summary["recommendations"]:
            summary["recommendations"].append("Performance optimization is working well!")
        
        return summary


async def main():
    """Main test execution function."""
    print("🚀 Flight Booking API Performance Optimization Test")
    print("=" * 60)
    print("Testing optimized endpoints for 3-second response target...")
    print()
    
    async with PerformanceTestSuite() as test_suite:
        results = await test_suite.run_all_tests()
        
        print("=" * 60)
        print("📋 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        if "test_summary" in results:
            summary = results["test_summary"]
            print(f"🎯 Status: {summary['performance_optimization_status']}")
            print(f"📊 Tests Run: {summary['total_tests_run']}")
            print()
            
            if summary["key_metrics"]:
                print("📈 Key Metrics:")
                for metric, value in summary["key_metrics"].items():
                    if "response" in metric or "time" in metric:
                        print(f"  • {metric}: {value:.2f}ms")
                    elif "rate" in metric:
                        print(f"  • {metric}: {value:.1%}")
                    else:
                        print(f"  • {metric}: {value}")
                print()
            
            if summary["recommendations"]:
                print("💡 Recommendations:")
                for rec in summary["recommendations"]:
                    print(f"  • {rec}")
                print()
        
        # Save detailed results
        with open("performance_test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print("📄 Detailed results saved to: performance_test_results.json")
        print("✅ Performance optimization testing completed!")


if __name__ == "__main__":
    asyncio.run(main())
