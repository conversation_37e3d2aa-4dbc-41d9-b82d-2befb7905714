from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.microservices.flight_service.detail_service.service import FlightDetail
from app.microservices.flight_service.schemas import RulesRequest

# Initialize the FastAPI router for fare rules service
router = APIRouter()
flight_detail = FlightDetail()  # Create an instance of the FlightDetail service

@router.post("/rules/")
async def fare_rules(request: RulesRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to retrieve fare rules associated with a flight based on the provided request data.

    Args:
        request_data (dict): A dictionary containing the necessary parameters
                             to fetch fare rules, which may include identifiers
                             like fare_id or TUI.

        background_tasks (BackgroundTasks): Allows scheduling background tasks
                                             to run after the response is returned.

    Returns:
        dict: A dictionary containing the fare rules information or an error message
              if the request fails or the fare rules are not found.
    """
    try:
        # Call the detail method of the FlightDetail service to get fare rules
        request_data = request.model_dump()
        result = flight_detail.detail(request_data)
        return result  # Return the retrieved fare rules
    except HTTPException as e:
        # If an HTTP exception occurs, raise it to return an appropriate error response
        raise e
    except Exception as e:
        # Handle any other exceptions that may arise
        raise HTTPException(status_code=500, detail=str(e))  # Return a 500 error with the exception message