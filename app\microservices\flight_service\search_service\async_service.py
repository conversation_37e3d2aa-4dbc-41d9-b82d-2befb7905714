"""
Fully async flight search service with advanced optimization features.
Implements async processing, request deduplication, circuit breaker, and intelligent caching.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from app.config import RedisCache
from app.microservices.flight_service.cache_service import (
    flight_cache_service,
    CacheStrategy,
    generate_search_cache_key
)
from app.microservices.flight_service.cache_warming.service import cache_warming_service
from app.microservices.flight_service.utils.async_provider_utils import (
    make_async_provider_call,
    CircuitBreakerOpenError,
    AsyncProviderError
)
from app.microservices.flight_service.utils.request_deduplication_optimized import (
    request_deduplicator
)
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import (
    flight_search_translate_client_to_provider
)
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import (
    SearchProviderResponseTranslator
)
from app.microservices.flight_service import config


class AsyncFlightSearch:
    """
    Fully async flight search service with advanced optimization features.
    """

    def __init__(self):
        self.cache_service = flight_cache_service
        self.redis_client = RedisCache.connection()
        self.warming_service = cache_warming_service
        self.deduplicator = request_deduplicator

        # Performance tracking
        self.performance_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "provider_calls": 0,
            "deduplicated_requests": 0,
            "circuit_breaker_blocks": 0,
            "errors": 0
        }

    async def search(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced async flight search with all optimization features.

        Args:
            request_data: Search request parameters

        Returns:
            Flight search results with performance metadata
        """
        start_time = time.time()
        self.performance_stats["total_requests"] += 1

        try:
            # Step 1: Generate cache key and request hash for deduplication
            cache_key = generate_search_cache_key(request_data)
            request_hash = self.deduplicator.generate_request_hash(
                request_data,
                "flight_search"
            )

            # Step 2: Check multi-layer cache first
            cached_results = await self._check_cache(cache_key)
            if cached_results:
                # Track search for popularity analysis
                response_time = time.time() - start_time
                await self.warming_service.track_search_request(
                    request_data, response_time, cache_hit=True
                )

                self.performance_stats["cache_hits"] += 1

                # Add performance metadata
                cached_results.update({
                    "sh_price": False,
                    "cache_hit": True,
                    "response_time_ms": round(response_time * 1000, 2),
                    "data_source": "cache",
                    "request_hash": request_hash
                })

                return cached_results

            # Step 3: Check for duplicate requests using optimized deduplication
            duplicate_result = await self.deduplicator.check_duplicate_request(request_hash)
            if duplicate_result:
                self.performance_stats["deduplicated_requests"] += 1
                return duplicate_result

            # Step 4: Register pending request and fetch from provider
            await self.deduplicator.register_pending_request(request_hash, cache_key, "flight_search")

            try:
                result = await self._fetch_from_provider(request_data, cache_key, start_time)

                # Mark request as completed
                await self.deduplicator.complete_request(request_hash, result)

                return result

            except Exception as e:
                # Mark request as failed
                await self.deduplicator.fail_request(request_hash, str(e))
                return await self._create_background_response(request_data, cache_key, start_time)

        except Exception as e:
            self.performance_stats["errors"] += 1
            print(f"Async search error: {str(e)}")
            return await self._create_error_response(request_data, str(e), start_time)

    async def _check_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Check multi-layer cache for existing results.

        Args:
            cache_key: Cache key to check

        Returns:
            Cached results if found, None otherwise
        """
        try:
            # For now, check Redis cache directly
            # In full async implementation, this would use the async cache service
            cached_results = self.redis_client.get_cache(cache_key)

            if cached_results:
                # Trigger background refresh if data is getting stale
                await self._trigger_background_refresh_if_needed(cached_results, cache_key)
                return cached_results

            return None

        except Exception as e:
            print(f"Cache check error: {str(e)}")
            return None

    async def _fetch_from_provider(
        self,
        request_data: Dict[str, Any],
        cache_key: str,
        start_time: float
    ) -> Dict[str, Any]:
        """
        Fetch flight data from provider with circuit breaker protection.

        Args:
            request_data: Search request data
            cache_key: Cache key for storing results
            start_time: Request start time

        Returns:
            Provider response data
        """
        try:
            self.performance_stats["provider_calls"] += 1

            # Translate request for provider
            provider_payload = flight_search_translate_client_to_provider(request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"

            # Make async provider call with circuit breaker protection
            provider_response = await make_async_provider_call(
                provider_api_url,
                provider_payload,
                "tripjack"
            )

            # Translate response
            translator = SearchProviderResponseTranslator(provider_response, request_data)
            client_response = translator.translate()

            # Add metadata
            response_time = time.time() - start_time
            client_response.update({
                "TUI": cache_key,
                "sh_price": False,
                "cache_hit": False,
                "response_time_ms": round(response_time * 1000, 2),
                "data_source": "provider_async",
                "cached_at": datetime.now(timezone.utc).isoformat()
            })

            # Cache the results asynchronously
            asyncio.create_task(self._cache_results(cache_key, client_response))

            # Track search for popularity analysis
            await self.warming_service.track_search_request(
                request_data, response_time, cache_hit=False
            )

            return client_response

        except CircuitBreakerOpenError:
            self.performance_stats["circuit_breaker_blocks"] += 1
            print("Circuit breaker is open, falling back to background task")
            return await self._create_background_response(request_data, cache_key, start_time)

        except AsyncProviderError as e:
            print(f"Provider error: {str(e)}")
            return await self._create_background_response(request_data, cache_key, start_time)

    async def _cache_results(self, cache_key: str, results: Dict[str, Any]):
        """
        Cache results asynchronously.

        Args:
            cache_key: Cache key
            results: Results to cache
        """
        try:
            self.redis_client.set_cache(
                cache_key,
                results,
                config.cache_timer.get('FLIGHT_SEARCH', 900)
            )
        except Exception as e:
            print(f"Async cache write error: {str(e)}")

    async def _trigger_background_refresh_if_needed(
        self,
        cached_results: Dict[str, Any],
        cache_key: str
    ):
        """
        Trigger background refresh if cached data is getting stale.

        Args:
            cached_results: Current cached results
            cache_key: Cache key
        """
        try:
            cached_at_str = cached_results.get("cached_at")
            if cached_at_str:
                cached_at = datetime.fromisoformat(cached_at_str.replace('Z', '+00:00'))
                age_minutes = (datetime.now(timezone.utc) - cached_at).total_seconds() / 60

                # Refresh if data is older than 10 minutes
                if age_minutes > 10:
                    # Import here to avoid circular imports
                    from app.microservices.flight_service.search_service.tasks import fetch_flight_search_task

                    # Extract original request data from cache key or use default
                    request_data = self._extract_request_from_cache_key(cache_key)
                    fetch_flight_search_task.delay("RefreshProvider", request_data, cache_key)

        except Exception as e:
            print(f"Background refresh trigger error: {str(e)}")

    def _extract_request_from_cache_key(self, cache_key: str) -> Dict[str, Any]:
        """
        Extract basic request data from cache key for background refresh.

        Args:
            cache_key: Cache key to parse

        Returns:
            Basic request data
        """
        try:
            # Parse cache key format: flight_search:ROUTE:DATE:HASH
            parts = cache_key.split(":")
            if len(parts) >= 3:
                route_part = parts[1]  # FROM-TO
                date_part = parts[2]   # DATE

                if "-" in route_part:
                    from_code, to_code = route_part.split("-", 1)
                    return {
                        "Trips": [{"From": from_code, "To": to_code, "OnwardDate": date_part}],
                        "ADT": 1,
                        "CHD": 0,
                        "INF": 0,
                        "Cabin": "E",
                        "FareType": "REGULAR"
                    }
        except Exception as e:
            print(f"Error extracting request from cache key: {str(e)}")

        # Return default request data
        return {
            "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-01-15"}],
            "ADT": 1,
            "CHD": 0,
            "INF": 0,
            "Cabin": "E",
            "FareType": "REGULAR"
        }

    async def _create_background_response(
        self,
        request_data: Dict[str, Any],
        cache_key: str,
        start_time: float
    ) -> Dict[str, Any]:
        """
        Create response for background processing.

        Args:
            request_data: Original request data
            cache_key: Cache key
            start_time: Request start time

        Returns:
            Background processing response
        """
        # Import here to avoid circular imports
        from app.microservices.flight_service.search_service.tasks import fetch_flight_search_task

        # Trigger background task
        fetch_flight_search_task.delay("BackgroundProvider", request_data, cache_key)

        return {
            "TUI": cache_key,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": ["Search in progress. Please check back in a few moments."],
            "Trips": [],
            "Code": "202",
            "Msg": ["Flight search initiated. Results will be available shortly."],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "background_task",
            "search_status": "processing"
        }

    async def _create_error_response(
        self,
        request_data: Dict[str, Any],
        error_message: str,
        start_time: float
    ) -> Dict[str, Any]:
        """
        Create standardized error response.

        Args:
            request_data: Original request data
            error_message: Error message
            start_time: Request start time

        Returns:
            Error response
        """
        return {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Internal server error occurred"],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "error",
            "error_details": error_message
        }

    async def search_list(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced async search list retrieval.

        Args:
            request_data: Request data containing TUI

        Returns:
            Cached search results
        """
        cache_key = request_data.get('TUI')

        if not cache_key:
            return await self._create_error_response(
                request_data,
                "Missing TUI parameter",
                time.time()
            )

        try:
            # Attempt to retrieve results from cache
            results = self.redis_client.get_cache(cache_key)

            if not results:
                return {
                    "TUI": cache_key,
                    "Completed": False,
                    "CeilingInfo": None,
                    "CurrencyCode": "INR",
                    "Notices": ["Search results have expired. Please perform a new search."],
                    "Trips": None,
                    "Code": "404",
                    "Msg": ["Search results not found or expired"]
                }

            # Add metadata and return results
            results.update({
                "TUI": cache_key,
                "sh_price": True,
                "retrieved_at": datetime.now(timezone.utc).isoformat(),
                "data_source": "cache_retrieval"
            })

            return results

        except Exception as e:
            print(f"Async search list error: {str(e)}")
            return await self._create_error_response(request_data, str(e), time.time())

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_requests = self.performance_stats["total_requests"]

        return {
            **self.performance_stats,
            "cache_hit_rate": (
                self.performance_stats["cache_hits"] / total_requests
                if total_requests > 0 else 0
            ),
            "deduplication_rate": (
                self.performance_stats["deduplicated_requests"] / total_requests
                if total_requests > 0 else 0
            ),
            "error_rate": (
                self.performance_stats["errors"] / total_requests
                if total_requests > 0 else 0
            )
        }


# Global async flight search instance
async_flight_search = AsyncFlightSearch()
