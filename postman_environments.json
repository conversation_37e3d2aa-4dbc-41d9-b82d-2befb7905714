{"environments": [{"id": "local-development", "name": "Local Development", "values": [{"key": "base_url", "value": "http://localhost:8000/apis", "enabled": true, "type": "default"}, {"key": "api_key", "value": "test-api-key-12345", "enabled": true, "type": "default"}, {"key": "saved_tui", "value": "", "enabled": true, "type": "default"}, {"key": "saved_fare_id", "value": "", "enabled": true, "type": "default"}, {"key": "future_date", "value": "2024-02-15", "enabled": true, "type": "default"}, {"key": "websocket_url", "value": "ws://localhost:8000/apis/advanced/dashboard/realtime", "enabled": true, "type": "default"}, {"key": "test_origin", "value": "DEL", "enabled": true, "type": "default"}, {"key": "test_destination", "value": "BOM", "enabled": true, "type": "default"}, {"key": "test_adults", "value": "1", "enabled": true, "type": "default"}, {"key": "test_children", "value": "0", "enabled": true, "type": "default"}, {"key": "test_infants", "value": "0", "enabled": true, "type": "default"}, {"key": "test_cabin", "value": "E", "enabled": true, "type": "default"}, {"key": "test_fare_type", "value": "REGULAR", "enabled": true, "type": "default"}], "_postman_variable_scope": "environment"}, {"id": "staging-environment", "name": "Staging Environment", "values": [{"key": "base_url", "value": "https://staging-api.flightbooking.com/v4", "enabled": true, "type": "default"}, {"key": "api_key", "value": "staging-api-key-67890", "enabled": true, "type": "secret"}, {"key": "saved_tui", "value": "", "enabled": true, "type": "default"}, {"key": "saved_fare_id", "value": "", "enabled": true, "type": "default"}, {"key": "future_date", "value": "2024-02-15", "enabled": true, "type": "default"}, {"key": "websocket_url", "value": "wss://staging-api.flightbooking.com/v4/advanced/dashboard/realtime", "enabled": true, "type": "default"}, {"key": "test_origin", "value": "DEL", "enabled": true, "type": "default"}, {"key": "test_destination", "value": "BOM", "enabled": true, "type": "default"}, {"key": "test_adults", "value": "1", "enabled": true, "type": "default"}, {"key": "test_children", "value": "0", "enabled": true, "type": "default"}, {"key": "test_infants", "value": "0", "enabled": true, "type": "default"}, {"key": "test_cabin", "value": "E", "enabled": true, "type": "default"}, {"key": "test_fare_type", "value": "REGULAR", "enabled": true, "type": "default"}], "_postman_variable_scope": "environment"}, {"id": "production-environment", "name": "Production Environment", "values": [{"key": "base_url", "value": "https://api.flightbooking.com/v4", "enabled": true, "type": "default"}, {"key": "api_key", "value": "production-api-key-abcdef", "enabled": true, "type": "secret"}, {"key": "saved_tui", "value": "", "enabled": true, "type": "default"}, {"key": "saved_fare_id", "value": "", "enabled": true, "type": "default"}, {"key": "future_date", "value": "2024-02-15", "enabled": true, "type": "default"}, {"key": "websocket_url", "value": "wss://api.flightbooking.com/v4/advanced/dashboard/realtime", "enabled": true, "type": "default"}, {"key": "test_origin", "value": "DEL", "enabled": true, "type": "default"}, {"key": "test_destination", "value": "BOM", "enabled": true, "type": "default"}, {"key": "test_adults", "value": "1", "enabled": true, "type": "default"}, {"key": "test_children", "value": "0", "enabled": true, "type": "default"}, {"key": "test_infants", "value": "0", "enabled": true, "type": "default"}, {"key": "test_cabin", "value": "E", "enabled": true, "type": "default"}, {"key": "test_fare_type", "value": "REGULAR", "enabled": true, "type": "default"}], "_postman_variable_scope": "environment"}, {"id": "performance-testing", "name": "Performance Testing", "values": [{"key": "base_url", "value": "http://localhost:8000/apis", "enabled": true, "type": "default"}, {"key": "api_key", "value": "performance-test-key", "enabled": true, "type": "default"}, {"key": "saved_tui", "value": "", "enabled": true, "type": "default"}, {"key": "saved_fare_id", "value": "", "enabled": true, "type": "default"}, {"key": "future_date", "value": "2024-02-15", "enabled": true, "type": "default"}, {"key": "load_test_iterations", "value": "100", "enabled": true, "type": "default"}, {"key": "concurrent_users", "value": "10", "enabled": true, "type": "default"}, {"key": "response_time_threshold", "value": "1000", "enabled": true, "type": "default"}, {"key": "cache_hit_rate_threshold", "value": "90", "enabled": true, "type": "default"}, {"key": "error_rate_threshold", "value": "1", "enabled": true, "type": "default"}], "_postman_variable_scope": "environment"}], "globals": [{"key": "timestamp", "value": "", "enabled": true, "type": "default"}, {"key": "random_id", "value": "", "enabled": true, "type": "default"}, {"key": "test_run_id", "value": "", "enabled": true, "type": "default"}], "test_data": {"popular_routes": [{"from": "DEL", "to": "BOM", "name": "Delhi to Mumbai"}, {"from": "BOM", "to": "DEL", "name": "Mumbai to Delhi"}, {"from": "DEL", "to": "BLR", "name": "Delhi to Bangalore"}, {"from": "BLR", "to": "DEL", "name": "Bangalore to Delhi"}, {"from": "BOM", "to": "BLR", "name": "Mumbai to Bangalore"}, {"from": "BLR", "to": "BOM", "name": "Bangalore to Mumbai"}, {"from": "DEL", "to": "MAA", "name": "Delhi to Chennai"}, {"from": "MAA", "to": "DEL", "name": "Chennai to Delhi"}, {"from": "DEL", "to": "CCU", "name": "Delhi to Kolkata"}, {"from": "CCU", "to": "DEL", "name": "Kolkata to Delhi"}], "test_passengers": [{"scenario": "single_adult", "adults": 1, "children": 0, "infants": 0}, {"scenario": "family_with_children", "adults": 2, "children": 2, "infants": 0}, {"scenario": "business_group", "adults": 4, "children": 0, "infants": 0}, {"scenario": "family_with_infant", "adults": 2, "children": 1, "infants": 1}], "cabin_classes": [{"code": "E", "name": "Economy"}, {"code": "B", "name": "Business"}, {"code": "F", "name": "First"}], "fare_types": [{"code": "REGULAR", "name": "Regular"}, {"code": "STUDENT", "name": "Student"}, {"code": "SENIOR", "name": "Senior Citizen"}, {"code": "MILITARY", "name": "Military"}], "airlines": [{"code": "AI", "name": "Air India"}, {"code": "6E", "name": "IndiGo"}, {"code": "SG", "name": "SpiceJet"}, {"code": "UK", "name": "Vistara"}, {"code": "G8", "name": "Go First"}], "test_dates": ["2024-02-15", "2024-02-16", "2024-02-17", "2024-02-18", "2024-02-19", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-24"]}, "pre_request_scripts": {"set_dynamic_variables": ["// Set current timestamp", "pm.globals.set('timestamp', new Date().toISOString());", "", "// Set random test ID", "pm.globals.set('random_id', 'TEST_' + Math.floor(Math.random() * 100000));", "", "// Set test run ID (once per collection run)", "if (!pm.globals.get('test_run_id')) {", "    pm.globals.set('test_run_id', 'RUN_' + Date.now());", "}", "", "// Set future date (30 days from now)", "const futureDate = new Date();", "futureDate.setDate(futureDate.getDate() + 30);", "pm.environment.set('future_date', futureDate.toISOString().split('T')[0]);"], "performance_monitoring": ["// Start performance timer", "pm.globals.set('request_start_time', Date.now());"]}, "test_scripts": {"common_validations": ["// Common response validations", "pm.test('Response time is acceptable', function () {", "    const threshold = pm.environment.get('response_time_threshold') || 5000;", "    pm.expect(pm.response.responseTime).to.be.below(parseInt(threshold));", "});", "", "pm.test('Response has valid JSON', function () {", "    pm.response.to.be.json;", "});", "", "pm.test('Response status is successful', function () {", "    pm.response.to.have.status(200);", "});", "", "// Save important response data", "if (pm.response.json().TUI) {", "    pm.environment.set('saved_tui', pm.response.json().TUI);", "}", "", "const responseJson = pm.response.json();", "if (responseJson.Results && responseJson.Results.length > 0 && responseJson.Results[0].FareId) {", "    pm.environment.set('saved_fare_id', responseJson.Results[0].FareId);", "}"], "performance_validations": ["// Performance-specific validations", "const startTime = pm.globals.get('request_start_time');", "if (startTime) {", "    const responseTime = Date.now() - parseInt(startTime);", "    console.log('Measured response time:', responseTime + 'ms');", "}", "", "// Check cache performance", "const cacheStatus = pm.response.headers.get('X-Cache-Status');", "if (cacheStatus) {", "    pm.test('Cache status is present', function () {", "        pm.expect(['HIT', 'MISS', 'REFRESH']).to.include(cacheStatus);", "    });", "}", "", "// Check performance score", "const performanceScore = pm.response.headers.get('X-Performance-Score');", "if (performanceScore) {", "    pm.test('Performance score is acceptable', function () {", "        pm.expect(parseInt(performanceScore)).to.be.above(70);", "    });", "}"], "cache_validations": ["// Cache-specific validations", "pm.test('Cache statistics are valid', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('overall_performance');", "    pm.expect(jsonData.overall_performance).to.have.property('hit_rate_percentage');", "    pm.expect(jsonData.overall_performance.hit_rate_percentage).to.be.a('number');", "});", "", "// Check cache hit rate threshold", "const threshold = pm.environment.get('cache_hit_rate_threshold') || 80;", "const jsonData = pm.response.json();", "if (jsonData.overall_performance && jsonData.overall_performance.hit_rate_percentage) {", "    pm.test('Cache hit rate meets threshold', function () {", "        pm.expect(jsonData.overall_performance.hit_rate_percentage).to.be.above(parseInt(threshold));", "    });", "}"]}}