"""
Compatibility stub for request deduplication.
Redirects to the optimized implementation.
"""

# Import everything from the optimized version for backward compatibility
from app.microservices.flight_service.utils.request_deduplication_optimized import (
    PendingRequest,
    RequestDeduplicator,
    request_deduplicator
)

# Legacy exception for backward compatibility
class RequestExpiredError(Exception):
    """Exception raised when a deduplicated request expires."""
    pass

# Export all for backward compatibility
__all__ = [
    'PendingRequest',
    'RequestDeduplicator', 
    'request_deduplicator',
    'RequestExpiredError'
]
