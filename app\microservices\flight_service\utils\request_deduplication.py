"""
Request deduplication service to prevent duplicate API calls and improve performance.
Implements intelligent request coalescing and result sharing.
"""

import asyncio
import time
import hashlib
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Set, Callable, Awaitable, List
from dataclasses import dataclass, field
from enum import Enum


class RequestStatus(Enum):
    """Status of a deduplicated request."""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


@dataclass
class PendingRequest:
    """Information about a pending request."""
    request_id: str
    request_hash: str
    created_at: float
    status: RequestStatus = RequestStatus.PENDING
    result: Optional[Any] = None
    error: Optional[Exception] = None
    waiters: Set[asyncio.Future] = field(default_factory=set)
    expires_at: float = field(default_factory=lambda: time.time() + 300)  # 5 minutes


class RequestDeduplicator:
    """
    Service for deduplicating concurrent requests to prevent redundant API calls.
    """
    
    def __init__(self, default_ttl: int = 300):
        self.pending_requests: Dict[str, PendingRequest] = {}
        self.default_ttl = default_ttl  # Default TTL in seconds
        self.stats = {
            "total_requests": 0,
            "deduplicated_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "expired_requests": 0,
            "cache_hits": 0
        }
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start background cleanup task."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_requests())
    
    async def _cleanup_expired_requests(self):
        """Background task to clean up expired requests."""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                current_time = time.time()
                expired_keys = []
                
                for request_hash, pending_request in self.pending_requests.items():
                    if current_time > pending_request.expires_at:
                        expired_keys.append(request_hash)
                        pending_request.status = RequestStatus.EXPIRED
                        
                        # Notify waiters about expiration
                        for waiter in pending_request.waiters:
                            if not waiter.done():
                                waiter.set_exception(RequestExpiredError("Request expired"))
                
                # Remove expired requests
                for key in expired_keys:
                    del self.pending_requests[key]
                    self.stats["expired_requests"] += 1
                    
            except Exception as e:
                print(f"Error in cleanup task: {str(e)}")
    
    def generate_request_hash(self, request_data: Dict[str, Any], context: str = "") -> str:
        """
        Generate a unique hash for request deduplication.
        
        Args:
            request_data: Request parameters
            context: Additional context for hash generation
            
        Returns:
            Unique hash string
        """
        # Create a normalized representation of the request
        normalized_data = {
            "context": context,
            "data": request_data
        }
        
        # Generate hash
        serialized = json.dumps(normalized_data, sort_keys=True, separators=(',', ':'))
        return hashlib.sha256(serialized.encode()).hexdigest()[:16]
    
    async def deduplicate_request(
        self,
        request_hash: str,
        request_func: Callable[[], Awaitable[Any]],
        ttl: Optional[int] = None
    ) -> Any:
        """
        Execute request with deduplication.
        
        Args:
            request_hash: Unique hash for the request
            request_func: Async function to execute if not deduplicated
            ttl: Time to live for the request in seconds
            
        Returns:
            Request result
            
        Raises:
            RequestExpiredError: If request expired
            Exception: Original exception from request_func
        """
        self.stats["total_requests"] += 1
        current_time = time.time()
        expires_at = current_time + (ttl or self.default_ttl)
        
        # Check if request is already pending
        if request_hash in self.pending_requests:
            pending_request = self.pending_requests[request_hash]
            
            # Check if request has expired
            if current_time > pending_request.expires_at:
                del self.pending_requests[request_hash]
                self.stats["expired_requests"] += 1
            else:
                # Request is still valid, wait for result
                self.stats["deduplicated_requests"] += 1
                return await self._wait_for_result(pending_request)
        
        # Create new pending request
        request_id = f"{request_hash}_{int(current_time)}"
        pending_request = PendingRequest(
            request_id=request_id,
            request_hash=request_hash,
            created_at=current_time,
            expires_at=expires_at
        )
        
        self.pending_requests[request_hash] = pending_request
        
        try:
            # Execute the request
            result = await request_func()
            
            # Mark as completed and store result
            pending_request.status = RequestStatus.COMPLETED
            pending_request.result = result
            self.stats["completed_requests"] += 1
            
            # Notify all waiters
            for waiter in pending_request.waiters:
                if not waiter.done():
                    waiter.set_result(result)
            
            return result
            
        except Exception as e:
            # Mark as failed and store error
            pending_request.status = RequestStatus.FAILED
            pending_request.error = e
            self.stats["failed_requests"] += 1
            
            # Notify all waiters about the error
            for waiter in pending_request.waiters:
                if not waiter.done():
                    waiter.set_exception(e)
            
            raise
        
        finally:
            # Clean up the pending request after a short delay
            asyncio.create_task(self._delayed_cleanup(request_hash, 30))
    
    async def _wait_for_result(self, pending_request: PendingRequest) -> Any:
        """
        Wait for a pending request to complete.
        
        Args:
            pending_request: The pending request to wait for
            
        Returns:
            Request result
            
        Raises:
            Exception: Original exception if request failed
        """
        # If already completed, return immediately
        if pending_request.status == RequestStatus.COMPLETED:
            self.stats["cache_hits"] += 1
            return pending_request.result
        elif pending_request.status == RequestStatus.FAILED:
            raise pending_request.error
        elif pending_request.status == RequestStatus.EXPIRED:
            raise RequestExpiredError("Request expired")
        
        # Create a future to wait for completion
        waiter = asyncio.Future()
        pending_request.waiters.add(waiter)
        
        try:
            # Wait for the request to complete
            result = await waiter
            self.stats["cache_hits"] += 1
            return result
        finally:
            # Remove waiter from the set
            pending_request.waiters.discard(waiter)
    
    async def _delayed_cleanup(self, request_hash: str, delay: int):
        """
        Clean up a request after a delay.
        
        Args:
            request_hash: Hash of the request to clean up
            delay: Delay in seconds before cleanup
        """
        await asyncio.sleep(delay)
        if request_hash in self.pending_requests:
            del self.pending_requests[request_hash]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get deduplication statistics.
        
        Returns:
            Statistics dictionary
        """
        total_requests = self.stats["total_requests"]
        deduplication_rate = (
            self.stats["deduplicated_requests"] / total_requests 
            if total_requests > 0 else 0
        )
        
        return {
            **self.stats,
            "pending_requests": len(self.pending_requests),
            "deduplication_rate": deduplication_rate,
            "active_cleanup_task": self._cleanup_task is not None and not self._cleanup_task.done()
        }
    
    def get_pending_requests_info(self) -> List[Dict[str, Any]]:
        """
        Get information about currently pending requests.
        
        Returns:
            List of pending request information
        """
        current_time = time.time()
        return [
            {
                "request_id": req.request_id,
                "request_hash": req.request_hash,
                "status": req.status.value,
                "created_at": req.created_at,
                "expires_at": req.expires_at,
                "age_seconds": current_time - req.created_at,
                "waiters_count": len(req.waiters),
                "has_result": req.result is not None,
                "has_error": req.error is not None
            }
            for req in self.pending_requests.values()
        ]
    
    async def clear_expired_requests(self) -> int:
        """
        Manually clear expired requests.
        
        Returns:
            Number of requests cleared
        """
        current_time = time.time()
        expired_keys = []
        
        for request_hash, pending_request in self.pending_requests.items():
            if current_time > pending_request.expires_at:
                expired_keys.append(request_hash)
                pending_request.status = RequestStatus.EXPIRED
                
                # Notify waiters about expiration
                for waiter in pending_request.waiters:
                    if not waiter.done():
                        waiter.set_exception(RequestExpiredError("Request expired"))
        
        # Remove expired requests
        for key in expired_keys:
            del self.pending_requests[key]
            self.stats["expired_requests"] += 1
        
        return len(expired_keys)
    
    async def shutdown(self):
        """Shutdown the deduplicator and cleanup resources."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Cancel all pending waiters
        for pending_request in self.pending_requests.values():
            for waiter in pending_request.waiters:
                if not waiter.done():
                    waiter.cancel()
        
        self.pending_requests.clear()


class RequestExpiredError(Exception):
    """Exception raised when a deduplicated request expires."""
    pass


# Global request deduplicator instance
request_deduplicator = RequestDeduplicator(default_ttl=300)  # 5 minutes default TTL
