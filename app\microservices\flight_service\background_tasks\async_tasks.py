"""
Enhanced background tasks for async processing with improved error handling and monitoring.
Implements intelligent task scheduling, retry logic, and performance tracking.
"""

import asyncio
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from celery import Celery
from celery.exceptions import Retry

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.cache_warming.service import cache_warming_service
from app.microservices.flight_service.utils.async_provider_utils import make_async_provider_call
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import (
    flight_search_translate_client_to_provider
)
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import (
    SearchProviderResponseTranslator
)
from app.microservices.flight_service.detail_service.providers.tripjack.request_creator import (
    review_translate_client_to_provider
)
from app.microservices.flight_service.detail_service.providers.tripjack.response_converter import (
    review_translate_provider_to_client
)
from app.microservices.flight_service import config


# Create Celery app instance
celery_app = Celery('flight_service_async_tasks')


class AsyncTaskManager:
    """
    Manager for async background tasks with enhanced error handling and monitoring.
    """
    
    def __init__(self):
        self.redis_client = RedisCache.connection()
        self.task_stats = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "retried_tasks": 0,
            "cache_refresh_tasks": 0,
            "warming_tasks": 0
        }
    
    async def execute_async_search_task(
        self,
        provider_name: str,
        request_data: Dict[str, Any],
        cache_key: str,
        task_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute async flight search task with enhanced error handling.
        
        Args:
            provider_name: Provider identifier
            request_data: Search request data
            cache_key: Cache key for storing results
            task_context: Additional task context
            
        Returns:
            Task execution results
        """
        start_time = time.time()
        self.task_stats["total_tasks"] += 1
        
        try:
            # Check if this is a cache refresh task
            is_refresh = task_context and task_context.get("type") == "refresh"
            if is_refresh:
                self.task_stats["cache_refresh_tasks"] += 1
            
            # Translate request for provider
            provider_payload = flight_search_translate_client_to_provider(request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"
            
            # Make async provider call
            provider_response = await make_async_provider_call(
                provider_api_url,
                provider_payload,
                "tripjack"
            )
            
            # Translate response
            translator = SearchProviderResponseTranslator(provider_response, request_data)
            client_response = translator.translate()
            
            # Add task metadata
            execution_time = time.time() - start_time
            client_response.update({
                "TUI": cache_key,
                "cached_at": datetime.now(timezone.utc).isoformat(),
                "task_execution_time": execution_time,
                "provider_name": provider_name,
                "background_task": True,
                "is_refresh": is_refresh
            })
            
            # Store in cache
            cache_ttl = config.cache_timer.get('FLIGHT_SEARCH', 900)
            self.redis_client.set_cache(cache_key, client_response, cache_ttl)
            
            # Track search for popularity analysis
            await cache_warming_service.track_search_request(
                request_data, execution_time, cache_hit=False
            )
            
            self.task_stats["successful_tasks"] += 1
            
            return {
                "status": "success",
                "cache_key": cache_key,
                "execution_time": execution_time,
                "provider_name": provider_name,
                "is_refresh": is_refresh
            }
            
        except Exception as e:
            self.task_stats["failed_tasks"] += 1
            error_message = str(e)
            
            # Store error information in cache for debugging
            error_response = {
                "TUI": cache_key,
                "error": error_message,
                "task_failed_at": datetime.now(timezone.utc).isoformat(),
                "provider_name": provider_name,
                "background_task": True,
                "status": "failed"
            }
            
            # Store error with shorter TTL
            self.redis_client.set_cache(f"{cache_key}_error", error_response, 300)
            
            print(f"Async search task failed: {error_message}")
            
            return {
                "status": "failed",
                "error": error_message,
                "cache_key": cache_key,
                "provider_name": provider_name
            }
    
    async def execute_async_detail_task(
        self,
        provider_name: str,
        request_data: Dict[str, Any],
        tui: str,
        context: Dict[str, Any],
        fare_id: str,
        task_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute async flight detail task with enhanced error handling.
        
        Args:
            provider_name: Provider identifier
            request_data: Detail request data
            tui: Transaction unique identifier
            context: Request context
            fare_id: Fare identifier
            task_context: Additional task context
            
        Returns:
            Task execution results
        """
        start_time = time.time()
        self.task_stats["total_tasks"] += 1
        
        try:
            # Check if this is a cache refresh task
            is_refresh = task_context and task_context.get("type") == "refresh"
            if is_refresh:
                self.task_stats["cache_refresh_tasks"] += 1
            
            # Translate request for provider
            provider_payload = review_translate_client_to_provider(context, request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/review"
            
            # Make async provider call
            provider_response = await make_async_provider_call(
                provider_api_url,
                provider_payload,
                "tripjack"
            )
            
            # Translate response
            client_response = review_translate_provider_to_client(
                request_data, provider_response, context
            )
            
            # Add task metadata
            execution_time = time.time() - start_time
            client_response.update({
                "TUI": tui,
                "cached_at": datetime.now(timezone.utc).isoformat(),
                "task_execution_time": execution_time,
                "provider_name": provider_name,
                "background_task": True,
                "is_refresh": is_refresh,
                "fare_id": fare_id
            })
            
            # Store in cache with both TUI and new cache key
            cache_ttl = config.cache_timer.get('FLIGHT_DETAIL', 300)
            self.redis_client.set_cache(tui, client_response, cache_ttl)
            
            # Also store with new cache key format
            from app.microservices.flight_service.cache_service import generate_detail_cache_key
            cache_key = generate_detail_cache_key(fare_id, request_data)
            self.redis_client.set_cache(cache_key, client_response, cache_ttl)
            
            self.task_stats["successful_tasks"] += 1
            
            return {
                "status": "success",
                "tui": tui,
                "cache_key": cache_key,
                "execution_time": execution_time,
                "provider_name": provider_name,
                "is_refresh": is_refresh
            }
            
        except Exception as e:
            self.task_stats["failed_tasks"] += 1
            error_message = str(e)
            
            # Store error information in cache for debugging
            error_response = {
                "TUI": tui,
                "error": error_message,
                "task_failed_at": datetime.now(timezone.utc).isoformat(),
                "provider_name": provider_name,
                "background_task": True,
                "status": "failed",
                "fare_id": fare_id
            }
            
            # Store error with shorter TTL
            self.redis_client.set_cache(f"{tui}_error", error_response, 300)
            
            print(f"Async detail task failed: {error_message}")
            
            return {
                "status": "failed",
                "error": error_message,
                "tui": tui,
                "provider_name": provider_name
            }
    
    async def execute_cache_warming_task(
        self,
        warming_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute cache warming task.
        
        Args:
            warming_config: Cache warming configuration
            
        Returns:
            Warming task results
        """
        start_time = time.time()
        self.task_stats["total_tasks"] += 1
        self.task_stats["warming_tasks"] += 1
        
        try:
            max_routes = warming_config.get("max_routes", 10)
            future_days = warming_config.get("future_days", 3)
            
            # Execute cache warming
            result = await cache_warming_service.warm_popular_routes(max_routes, future_days)
            
            execution_time = time.time() - start_time
            result.update({
                "execution_time": execution_time,
                "task_completed_at": datetime.now(timezone.utc).isoformat()
            })
            
            self.task_stats["successful_tasks"] += 1
            
            return result
            
        except Exception as e:
            self.task_stats["failed_tasks"] += 1
            error_message = str(e)
            
            print(f"Cache warming task failed: {error_message}")
            
            return {
                "status": "failed",
                "error": error_message,
                "execution_time": time.time() - start_time
            }
    
    def get_task_stats(self) -> Dict[str, Any]:
        """Get task execution statistics."""
        total_tasks = self.task_stats["total_tasks"]
        
        return {
            **self.task_stats,
            "success_rate": (
                self.task_stats["successful_tasks"] / total_tasks 
                if total_tasks > 0 else 0
            ),
            "failure_rate": (
                self.task_stats["failed_tasks"] / total_tasks 
                if total_tasks > 0 else 0
            ),
            "retry_rate": (
                self.task_stats["retried_tasks"] / total_tasks 
                if total_tasks > 0 else 0
            )
        }


# Global async task manager
async_task_manager = AsyncTaskManager()


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def enhanced_fetch_flight_search_task(
    self,
    provider_name: str,
    request_data: Dict[str, Any],
    cache_key: str,
    task_context: Optional[Dict[str, Any]] = None
):
    """
    Enhanced Celery task for async flight search with retry logic.
    
    Args:
        provider_name: Provider identifier
        request_data: Search request data
        cache_key: Cache key for storing results
        task_context: Additional task context
    """
    try:
        # Run the async task
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            async_task_manager.execute_async_search_task(
                provider_name, request_data, cache_key, task_context
            )
        )
        
        loop.close()
        
        if result["status"] == "failed":
            # Retry on failure
            async_task_manager.task_stats["retried_tasks"] += 1
            raise self.retry(countdown=60, max_retries=3)
        
        return result
        
    except Exception as e:
        print(f"Enhanced search task error: {str(e)}")
        
        # Retry on exception
        if self.request.retries < self.max_retries:
            async_task_manager.task_stats["retried_tasks"] += 1
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        # Final failure
        return {
            "status": "failed",
            "error": str(e),
            "cache_key": cache_key,
            "retries_exhausted": True
        }


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def enhanced_fetch_flight_detail_task(
    self,
    provider_name: str,
    request_data: Dict[str, Any],
    tui: str,
    context: Dict[str, Any],
    fare_id: str,
    task_context: Optional[Dict[str, Any]] = None
):
    """
    Enhanced Celery task for async flight detail with retry logic.
    
    Args:
        provider_name: Provider identifier
        request_data: Detail request data
        tui: Transaction unique identifier
        context: Request context
        fare_id: Fare identifier
        task_context: Additional task context
    """
    try:
        # Run the async task
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            async_task_manager.execute_async_detail_task(
                provider_name, request_data, tui, context, fare_id, task_context
            )
        )
        
        loop.close()
        
        if result["status"] == "failed":
            # Retry on failure
            async_task_manager.task_stats["retried_tasks"] += 1
            raise self.retry(countdown=60, max_retries=3)
        
        return result
        
    except Exception as e:
        print(f"Enhanced detail task error: {str(e)}")
        
        # Retry on exception
        if self.request.retries < self.max_retries:
            async_task_manager.task_stats["retried_tasks"] += 1
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        # Final failure
        return {
            "status": "failed",
            "error": str(e),
            "tui": tui,
            "retries_exhausted": True
        }


@celery_app.task(bind=True, max_retries=2, default_retry_delay=300)
def enhanced_cache_warming_task(
    self,
    warming_config: Dict[str, Any]
):
    """
    Enhanced Celery task for cache warming.
    
    Args:
        warming_config: Cache warming configuration
    """
    try:
        # Run the async task
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            async_task_manager.execute_cache_warming_task(warming_config)
        )
        
        loop.close()
        
        return result
        
    except Exception as e:
        print(f"Enhanced warming task error: {str(e)}")
        
        # Retry on exception
        if self.request.retries < self.max_retries:
            async_task_manager.task_stats["retried_tasks"] += 1
            raise self.retry(countdown=300 * (2 ** self.request.retries))
        
        # Final failure
        return {
            "status": "failed",
            "error": str(e),
            "retries_exhausted": True
        }
