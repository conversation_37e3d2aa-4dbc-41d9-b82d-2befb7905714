from datetime import datetime, timedelta

def flight_search_translate_client_to_provider(client_request):
    """
    Translates a client flight search request into a provider-specific format.

    Args:
        client_request (dict): The client's flight search request containing
                               cabin class, passenger information, trip details,
                               and optional return date.

    Returns:
        dict: The formatted request to be sent to the flight provider.
    """

    # Mapping of cabin class codes from client input to provider format
    cabin_class_mapping = {
        "E": "ECONOMY",           # Map economy class code
        "B": "BUSINESS",          # Map business class code
        "PE": "PREMIUM_ECONOMY",  # Map premium economy class code
        "F": "FIRST"              # Map first class code
    }

    def validate_and_fix_date(date_str):
        """Validate and fix travel dates to ensure they're in the future"""
        if not date_str or date_str == "null":
            return None

        try:
            # Parse the date
            travel_date = datetime.strptime(date_str, '%Y-%m-%d')
            current_date = datetime.now()

            # If the date is in the past, use a future date (30 days from now)
            if travel_date <= current_date:
                future_date = current_date + timedelta(days=30)
                return future_date.strftime('%Y-%m-%d')

            return date_str
        except ValueError:
            # If date parsing fails, use a default future date
            future_date = datetime.now() + timedelta(days=30)
            return future_date.strftime('%Y-%m-%d')

    # Build route infos - only include routes with valid travel dates
    route_infos = []

    for trip in client_request.get("Trips", []):
        # Validate and fix the travel date
        travel_date = validate_and_fix_date(trip.get("OnwardDate"))

        if travel_date:
            route_infos.append({
                "fromCityOrAirport": {
                    "code": trip.get("From")
                },
                "toCityOrAirport": {
                    "code": trip.get("To")
                },
                "travelDate": travel_date
            })

    # Create the provider request dictionary
    provider_request = {
        "searchQuery": {  # The main search query for the provider
            "cabinClass": cabin_class_mapping.get(client_request.get("Cabin"), "ECONOMY"),  # Get cabin class or default to ECONOMY
            "paxInfo": {  # Passenger information section
                "ADULT": str(client_request.get("ADT", 1)),  # Number of adults as a string
                "CHILD": str(client_request.get("CHD", 0)),  # Number of children as a string
                "INFANT": str(client_request.get("INF", 0))   # Number of infants as a string
            },
            "routeInfos": route_infos,  # Only valid routes with travel dates
            "searchModifiers": {},  # Empty search modifiers
            "preferredAirline": []  # Empty preferred airlines list
        }
    }

    # Check if the request includes a return date for round trip
    if (len(client_request.get("Trips", [])) == 1 and
        "ReturnDate" in client_request["Trips"][0]):

        return_date = validate_and_fix_date(client_request["Trips"][0]["ReturnDate"])

        if return_date and route_infos:
            # Ensure return date is after outbound date
            outbound_date = datetime.strptime(route_infos[0]["travelDate"], '%Y-%m-%d')
            return_date_obj = datetime.strptime(return_date, '%Y-%m-%d')

            if return_date_obj <= outbound_date:
                # Set return date to be 1 day after outbound date
                return_date = (outbound_date + timedelta(days=1)).strftime('%Y-%m-%d')

            # Add return trip to the route info
            provider_request["searchQuery"]["routeInfos"].append({
                "fromCityOrAirport": {
                    "code": client_request["Trips"][0]["To"]  # Arrival code becomes departure for return
                },
                "toCityOrAirport": {
                    "code": client_request["Trips"][0]["From"]  # Departure code becomes arrival for return
                },
                "travelDate": return_date  # Set the validated return travel date
            })

    return provider_request  # Return the formatted request for the provider