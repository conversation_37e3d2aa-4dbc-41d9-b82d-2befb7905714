"""
Cache management routes for monitoring and controlling the flight service cache.
Provides endpoints for cache statistics, invalidation, and warming.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, List
from pydantic import BaseModel

from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.monitoring.cache_monitor import cache_monitor


# Pydantic models for request/response validation
class CacheInvalidationRequest(BaseModel):
    from_code: str
    to_code: str
    date: str


class CacheWarmingRequest(BaseModel):
    routes: List[Dict[str, Any]]


class CacheStatsResponse(BaseModel):
    hits: int
    misses: int
    invalidations: int
    refreshes: int
    hit_rate_percentage: float
    memory_cache_size: int
    total_requests: int


# Create router instance
router = APIRouter()


@router.get("/cache/stats", response_model=CacheStatsResponse)
async def get_cache_statistics():
    """
    Get comprehensive cache performance statistics.

    Returns:
        CacheStatsResponse: Cache performance metrics including hit rates,
                           memory usage, and request counts.
    """
    try:
        stats = await flight_cache_service.get_cache_statistics()
        return CacheStatsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cache stats: {str(e)}")


@router.post("/cache/invalidate")
async def invalidate_route_cache(request: CacheInvalidationRequest):
    """
    Invalidate cache entries for a specific route and date.

    Args:
        request: CacheInvalidationRequest containing route and date information

    Returns:
        dict: Invalidation results including count of invalidated entries
    """
    try:
        result = await flight_cache_service.invalidate_route_cache(
            request.from_code,
            request.to_code,
            request.date
        )
        return {
            "status": "success",
            "message": f"Invalidated {result['invalidated_entries']} cache entries",
            "details": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cache invalidation failed: {str(e)}")


@router.post("/cache/invalidate-all")
async def invalidate_all_cache():
    """
    Invalidate all cache entries (use with caution).

    Returns:
        dict: Confirmation of cache clearing operation
    """
    try:
        # Clear memory cache
        flight_cache_service.memory_cache.clear()

        # Clear Redis cache (this would need to be implemented in RedisCache)
        # For now, we'll just clear the memory cache

        return {
            "status": "success",
            "message": "All cache entries have been invalidated",
            "warning": "This operation may impact performance until cache is rebuilt"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")


@router.post("/cache/warm")
async def warm_cache(request: CacheWarmingRequest, background_tasks: BackgroundTasks):
    """
    Warm cache for popular routes.

    Args:
        request: CacheWarmingRequest containing list of routes to warm
        background_tasks: FastAPI background tasks for async processing

    Returns:
        dict: Confirmation that cache warming has been initiated
    """
    try:
        # Add cache warming task to background
        background_tasks.add_task(
            flight_cache_service.warm_popular_routes,
            request.routes
        )

        return {
            "status": "success",
            "message": f"Cache warming initiated for {len(request.routes)} routes",
            "routes_count": len(request.routes)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cache warming failed: {str(e)}")


@router.post("/cache/cleanup")
async def cleanup_expired_cache():
    """
    Clean up expired entries from memory cache.

    Returns:
        dict: Results of cleanup operation
    """
    try:
        cleaned_count = await flight_cache_service.cleanup_expired_memory_cache()

        return {
            "status": "success",
            "message": f"Cleaned up {cleaned_count} expired cache entries",
            "cleaned_entries": cleaned_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cache cleanup failed: {str(e)}")


@router.get("/cache/health")
async def cache_health_check():
    """
    Perform a health check on the cache system.

    Returns:
        dict: Health status of cache components
    """
    try:
        stats = await flight_cache_service.get_cache_statistics()

        # Determine health status based on metrics
        hit_rate = stats.get("hit_rate_percentage", 0)
        memory_size = stats.get("memory_cache_size", 0)

        health_status = "healthy"
        issues = []

        if hit_rate < 50:
            health_status = "warning"
            issues.append("Low cache hit rate")

        if memory_size > 10000:  # Arbitrary threshold
            health_status = "warning"
            issues.append("High memory cache usage")

        return {
            "status": health_status,
            "cache_hit_rate": hit_rate,
            "memory_cache_size": memory_size,
            "issues": issues,
            "timestamp": flight_cache_service.redis_client.connection.time()[0] if hasattr(flight_cache_service.redis_client, 'connection') else None
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "issues": ["Cache system error"]
        }


@router.get("/cache/keys/{pattern}")
async def get_cache_keys_by_pattern(pattern: str):
    """
    Get cache keys matching a specific pattern.

    Args:
        pattern: Pattern to match cache keys (e.g., "flight_search:*")

    Returns:
        dict: List of matching cache keys
    """
    try:
        # This would need to be implemented in the Redis client
        # For now, return memory cache keys that match the pattern
        memory_keys = [key for key in flight_cache_service.memory_cache.keys() if pattern.replace('*', '') in key]

        return {
            "status": "success",
            "pattern": pattern,
            "memory_cache_keys": memory_keys,
            "count": len(memory_keys)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cache keys: {str(e)}")


@router.get("/cache/config")
async def get_cache_configuration():
    """
    Get current cache configuration settings.

    Returns:
        dict: Current cache configuration
    """
    try:
        return {
            "status": "success",
            "configuration": {
                "cache_levels": {
                    "L1_MEMORY": {
                        "ttl_seconds": flight_cache_service.cache_ttl.get("L1_MEMORY", 30),
                        "description": "In-memory cache for hot data"
                    },
                    "L2_REDIS": {
                        "ttl_seconds": flight_cache_service.cache_ttl.get("L2_REDIS", 900),
                        "description": "Redis cache for warm data"
                    },
                    "L3_PERSISTENT": {
                        "ttl_seconds": flight_cache_service.cache_ttl.get("L3_PERSISTENT", 86400),
                        "description": "Long-term cache for cold data"
                    }
                },
                "current_memory_cache_size": len(flight_cache_service.memory_cache),
                "cache_strategies": ["WRITE_THROUGH", "WRITE_BEHIND", "CACHE_ASIDE", "REFRESH_AHEAD"]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cache configuration: {str(e)}")


@router.get("/cache/monitoring/metrics")
async def get_monitoring_metrics():
    """
    Get current cache monitoring metrics.

    Returns:
        dict: Current cache performance metrics
    """
    try:
        monitoring_result = await cache_monitor.run_monitoring_cycle()
        return {
            "status": "success",
            "monitoring": monitoring_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve monitoring metrics: {str(e)}")


@router.get("/cache/monitoring/summary")
async def get_performance_summary(hours: int = 1):
    """
    Get cache performance summary for specified time period.

    Args:
        hours: Number of hours to look back (default: 1)

    Returns:
        dict: Performance summary
    """
    try:
        if hours < 1 or hours > 24:
            raise HTTPException(status_code=400, detail="Hours must be between 1 and 24")

        summary = await cache_monitor.get_performance_summary(hours)
        return {
            "status": "success",
            "summary": summary
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve performance summary: {str(e)}")


@router.get("/cache/monitoring/alerts")
async def get_current_alerts():
    """
    Get current cache alerts and warnings.

    Returns:
        dict: Current alerts
    """
    try:
        # Collect current metrics and check for alerts
        metrics = await cache_monitor.collect_metrics()
        alerts = await cache_monitor.check_alerts(metrics)

        return {
            "status": "success",
            "alerts": alerts,
            "alert_count": len(alerts),
            "timestamp": metrics.timestamp
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve alerts: {str(e)}")
