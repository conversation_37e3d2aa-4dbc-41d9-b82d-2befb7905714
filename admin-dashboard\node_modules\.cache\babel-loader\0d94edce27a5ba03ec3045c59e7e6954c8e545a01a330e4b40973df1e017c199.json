{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PROJECTS\\\\fast_travel_backend\\\\admin-dashboard\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Divider, Typography, Box, Chip, useTheme, alpha } from '@mui/material';\nimport { Dashboard as DashboardIcon, Speed as PerformanceIcon, Storage as CacheIcon, Search as SearchIcon, Settings as SettingsIcon, Assessment as AnalyticsIcon, BugReport as ErrorIcon, CloudQueue as TripJackIcon, People as UsersIcon, Notifications as AlertsIcon, GetApp as ExportIcon, HealthAndSafety as HealthIcon } from '@mui/icons-material';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAppContext } from '../../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DRAWER_WIDTH = 280;\nconst Sidebar = () => {\n  _s();\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    state,\n    setSidebarOpen\n  } = useAppContext();\n  const getColorFromPalette = color => {\n    switch (color) {\n      case 'success':\n        return theme.palette.success.main;\n      case 'error':\n        return theme.palette.error.main;\n      case 'warning':\n        return theme.palette.warning.main;\n      case 'info':\n        return theme.palette.info.main;\n      case 'primary':\n        return theme.palette.primary.main;\n      case 'secondary':\n        return theme.palette.secondary.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n  const getChipColor = color => {\n    switch (color) {\n      case 'success':\n        return 'success';\n      case 'error':\n        return 'error';\n      case 'warning':\n        return 'warning';\n      case 'info':\n        return 'info';\n      case 'secondary':\n        return 'secondary';\n      default:\n        return 'primary';\n    }\n  };\n  const menuSections = [{\n    title: 'Overview',\n    items: [{\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 17\n      }, this),\n      path: '/'\n    }, {\n      id: 'health',\n      label: 'System Health',\n      icon: /*#__PURE__*/_jsxDEV(HealthIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 17\n      }, this),\n      path: '/health',\n      color: state.systemHealth.api === 'healthy' ? 'success' : 'error'\n    }]\n  }, {\n    title: 'Monitoring',\n    items: [{\n      id: 'performance',\n      label: 'Performance',\n      icon: /*#__PURE__*/_jsxDEV(PerformanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this),\n      path: '/performance'\n    }, {\n      id: 'cache',\n      label: 'Cache Management',\n      icon: /*#__PURE__*/_jsxDEV(CacheIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this),\n      path: '/cache'\n    }, {\n      id: 'searches',\n      label: 'Flight Searches',\n      icon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this),\n      path: '/searches',\n      badge: state.activeSearches.length\n    }, {\n      id: 'tripjack',\n      label: 'TripJack Integration',\n      icon: /*#__PURE__*/_jsxDEV(TripJackIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 17\n      }, this),\n      path: '/tripjack',\n      color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning'\n    }]\n  }, {\n    title: 'Analytics',\n    items: [{\n      id: 'analytics',\n      label: 'Route Analytics',\n      icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this),\n      path: '/analytics'\n    }, {\n      id: 'errors',\n      label: 'Error Logs',\n      icon: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this),\n      path: '/errors'\n    }, {\n      id: 'alerts',\n      label: 'System Alerts',\n      icon: /*#__PURE__*/_jsxDEV(AlertsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 17\n      }, this),\n      path: '/alerts',\n      badge: state.alerts.filter(alert => !alert.acknowledged).length,\n      color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning'\n    }]\n  }, {\n    title: 'Administration',\n    items: [{\n      id: 'settings',\n      label: 'Configuration',\n      icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this),\n      path: '/settings'\n    }, {\n      id: 'users',\n      label: 'User Management',\n      icon: /*#__PURE__*/_jsxDEV(UsersIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 17\n      }, this),\n      path: '/users'\n    }, {\n      id: 'export',\n      label: 'Data Export',\n      icon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this),\n      path: '/export'\n    }]\n  }];\n  const handleItemClick = path => {\n    navigate(path);\n    if (window.innerWidth < theme.breakpoints.values.md) {\n      setSidebarOpen(false);\n    }\n  };\n  const isSelected = path => {\n    if (path === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuItem = item => /*#__PURE__*/_jsxDEV(ListItem, {\n    disablePadding: true,\n    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isSelected(item.path),\n      onClick: () => handleItemClick(item.path),\n      sx: {\n        borderRadius: 1,\n        mx: 1,\n        mb: 0.5,\n        '&.Mui-selected': {\n          backgroundColor: alpha(theme.palette.primary.main, 0.12),\n          '&:hover': {\n            backgroundColor: alpha(theme.palette.primary.main, 0.16)\n          }\n        },\n        '&:hover': {\n          backgroundColor: alpha(theme.palette.action.hover, 0.08)\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        sx: {\n          color: isSelected(item.path) ? theme.palette.primary.main : theme.palette.text.secondary,\n          minWidth: 40\n        },\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: item.label,\n        sx: {\n          '& .MuiListItemText-primary': {\n            fontSize: '0.875rem',\n            fontWeight: isSelected(item.path) ? 600 : 400,\n            color: isSelected(item.path) ? theme.palette.primary.main : theme.palette.text.primary\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), item.badge !== undefined && item.badge > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n        label: item.badge,\n        size: \"small\",\n        color: getChipColor(item.color),\n        sx: {\n          height: 20,\n          fontSize: '0.75rem',\n          fontWeight: 600\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this), item.color && item.badge === undefined && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          backgroundColor: getColorFromPalette(item.color)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)\n  }, item.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: `1px solid ${theme.palette.divider}`,\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: 700,\n        children: \"Fast Travel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          opacity: 0.9\n        },\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: `1px solid ${theme.palette.divider}`\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 8,\n            height: 8,\n            borderRadius: '50%',\n            backgroundColor: state.wsConnected ? theme.palette.success.main : theme.palette.error.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: state.wsConnected ? 'Real-time Connected' : 'Offline Mode'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflow: 'auto',\n        py: 1\n      },\n      children: menuSections.map((section, index) => /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"overline\",\n          sx: {\n            px: 2,\n            py: 1,\n            display: 'block',\n            color: theme.palette.text.secondary,\n            fontSize: '0.75rem',\n            fontWeight: 600,\n            letterSpacing: 1\n          },\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          sx: {\n            px: 0\n          },\n          children: section.items.map(renderMenuItem)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this), index < menuSections.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mx: 2,\n            my: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 15\n        }, this)]\n      }, section.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderTop: `1px solid ${theme.palette.divider}`,\n        backgroundColor: alpha(theme.palette.background.paper, 0.8)\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        display: \"block\",\n        children: \"Version 1.0.0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        display: \"block\",\n        children: [\"Last updated: \", new Date().toLocaleDateString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 274,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"persistent\",\n      anchor: \"left\",\n      open: state.sidebarOpen,\n      sx: {\n        display: {\n          xs: 'none',\n          md: 'block'\n        },\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box',\n          borderRight: `1px solid ${theme.palette.divider}`,\n          backgroundColor: theme.palette.background.paper\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"temporary\",\n      anchor: \"left\",\n      open: state.sidebarOpen,\n      onClose: () => setSidebarOpen(false),\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile\n      },\n      sx: {\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box'\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"eo3I+AzjG+Wp98ovqyCajcgBx50=\", false, function () {\n  return [useTheme, useLocation, useNavigate, useAppContext];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Typography", "Box", "Chip", "useTheme", "alpha", "Dashboard", "DashboardIcon", "Speed", "PerformanceIcon", "Storage", "CacheIcon", "Search", "SearchIcon", "Settings", "SettingsIcon", "Assessment", "AnalyticsIcon", "BugReport", "ErrorIcon", "CloudQueue", "TripJackIcon", "People", "UsersIcon", "Notifications", "AlertsIcon", "GetApp", "ExportIcon", "HealthAndSafety", "HealthIcon", "useLocation", "useNavigate", "useAppContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DRAWER_WIDTH", "Sidebar", "_s", "theme", "location", "navigate", "state", "setSidebarOpen", "getColorFromPalette", "color", "palette", "success", "main", "error", "warning", "info", "primary", "secondary", "getChipColor", "menuSections", "title", "items", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "systemHealth", "api", "badge", "activeSearches", "length", "tripjack", "alerts", "filter", "alert", "acknowledged", "some", "severity", "handleItemClick", "window", "innerWidth", "breakpoints", "values", "md", "isSelected", "pathname", "startsWith", "renderMenuItem", "item", "disablePadding", "children", "selected", "onClick", "sx", "borderRadius", "mx", "mb", "backgroundColor", "action", "hover", "text", "min<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "undefined", "size", "height", "width", "drawerContent", "display", "flexDirection", "p", "borderBottom", "divider", "background", "dark", "variant", "opacity", "alignItems", "gap", "wsConnected", "flex", "overflow", "py", "map", "section", "index", "px", "letterSpacing", "dense", "my", "borderTop", "paper", "Date", "toLocaleDateString", "anchor", "open", "sidebarOpen", "xs", "boxSizing", "borderRight", "onClose", "ModalProps", "keepMounted", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PROJECTS/fast_travel_backend/admin-dashboard/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Typography,\n  Box,\n  Chip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  Speed as PerformanceIcon,\n  Storage as CacheIcon,\n  Search as SearchIcon,\n  Settings as SettingsIcon,\n  Assessment as AnalyticsIcon,\n  BugReport as ErrorIcon,\n  CloudQueue as TripJackIcon,\n  People as UsersIcon,\n  Notifications as AlertsIcon,\n  GetApp as ExportIcon,\n  HealthAndSafety as HealthIcon,\n} from '@mui/icons-material';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAppContext } from '../../contexts/AppContext';\n\nconst DRAWER_WIDTH = 280;\n\ninterface MenuItem {\n  id: string;\n  label: string;\n  icon: React.ReactElement;\n  path: string;\n  badge?: number;\n  color?: string;\n}\n\ninterface MenuSection {\n  title: string;\n  items: MenuItem[];\n}\n\nconst Sidebar: React.FC = () => {\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { state, setSidebarOpen } = useAppContext();\n\n  const getColorFromPalette = (color: string) => {\n    switch (color) {\n      case 'success':\n        return theme.palette.success.main;\n      case 'error':\n        return theme.palette.error.main;\n      case 'warning':\n        return theme.palette.warning.main;\n      case 'info':\n        return theme.palette.info.main;\n      case 'primary':\n        return theme.palette.primary.main;\n      case 'secondary':\n        return theme.palette.secondary.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n\n  const getChipColor = (color?: string): 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' => {\n    switch (color) {\n      case 'success':\n        return 'success';\n      case 'error':\n        return 'error';\n      case 'warning':\n        return 'warning';\n      case 'info':\n        return 'info';\n      case 'secondary':\n        return 'secondary';\n      default:\n        return 'primary';\n    }\n  };\n\n  const menuSections: MenuSection[] = [\n    {\n      title: 'Overview',\n      items: [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: <DashboardIcon />,\n          path: '/',\n        },\n        {\n          id: 'health',\n          label: 'System Health',\n          icon: <HealthIcon />,\n          path: '/health',\n          color: state.systemHealth.api === 'healthy' ? 'success' : 'error',\n        },\n      ],\n    },\n    {\n      title: 'Monitoring',\n      items: [\n        {\n          id: 'performance',\n          label: 'Performance',\n          icon: <PerformanceIcon />,\n          path: '/performance',\n        },\n        {\n          id: 'cache',\n          label: 'Cache Management',\n          icon: <CacheIcon />,\n          path: '/cache',\n        },\n        {\n          id: 'searches',\n          label: 'Flight Searches',\n          icon: <SearchIcon />,\n          path: '/searches',\n          badge: state.activeSearches.length,\n        },\n        {\n          id: 'tripjack',\n          label: 'TripJack Integration',\n          icon: <TripJackIcon />,\n          path: '/tripjack',\n          color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning',\n        },\n      ],\n    },\n    {\n      title: 'Analytics',\n      items: [\n        {\n          id: 'analytics',\n          label: 'Route Analytics',\n          icon: <AnalyticsIcon />,\n          path: '/analytics',\n        },\n        {\n          id: 'errors',\n          label: 'Error Logs',\n          icon: <ErrorIcon />,\n          path: '/errors',\n        },\n        {\n          id: 'alerts',\n          label: 'System Alerts',\n          icon: <AlertsIcon />,\n          path: '/alerts',\n          badge: state.alerts.filter(alert => !alert.acknowledged).length,\n          color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning',\n        },\n      ],\n    },\n    {\n      title: 'Administration',\n      items: [\n        {\n          id: 'settings',\n          label: 'Configuration',\n          icon: <SettingsIcon />,\n          path: '/settings',\n        },\n        {\n          id: 'users',\n          label: 'User Management',\n          icon: <UsersIcon />,\n          path: '/users',\n        },\n        {\n          id: 'export',\n          label: 'Data Export',\n          icon: <ExportIcon />,\n          path: '/export',\n        },\n      ],\n    },\n  ];\n\n  const handleItemClick = (path: string) => {\n    navigate(path);\n    if (window.innerWidth < theme.breakpoints.values.md) {\n      setSidebarOpen(false);\n    }\n  };\n\n  const isSelected = (path: string) => {\n    if (path === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuItem = (item: MenuItem) => (\n    <ListItem key={item.id} disablePadding>\n      <ListItemButton\n        selected={isSelected(item.path)}\n        onClick={() => handleItemClick(item.path)}\n        sx={{\n          borderRadius: 1,\n          mx: 1,\n          mb: 0.5,\n          '&.Mui-selected': {\n            backgroundColor: alpha(theme.palette.primary.main, 0.12),\n            '&:hover': {\n              backgroundColor: alpha(theme.palette.primary.main, 0.16),\n            },\n          },\n          '&:hover': {\n            backgroundColor: alpha(theme.palette.action.hover, 0.08),\n          },\n        }}\n      >\n        <ListItemIcon\n          sx={{\n            color: isSelected(item.path)\n              ? theme.palette.primary.main\n              : theme.palette.text.secondary,\n            minWidth: 40,\n          }}\n        >\n          {item.icon}\n        </ListItemIcon>\n        <ListItemText\n          primary={item.label}\n          sx={{\n            '& .MuiListItemText-primary': {\n              fontSize: '0.875rem',\n              fontWeight: isSelected(item.path) ? 600 : 400,\n              color: isSelected(item.path)\n                ? theme.palette.primary.main\n                : theme.palette.text.primary,\n            },\n          }}\n        />\n        {item.badge !== undefined && item.badge > 0 && (\n          <Chip\n            label={item.badge}\n            size=\"small\"\n            color={getChipColor(item.color)}\n            sx={{\n              height: 20,\n              fontSize: '0.75rem',\n              fontWeight: 600,\n            }}\n          />\n        )}\n        {item.color && item.badge === undefined && (\n          <Box\n            sx={{\n              width: 8,\n              height: 8,\n              borderRadius: '50%',\n              backgroundColor: getColorFromPalette(item.color),\n            }}\n          />\n        )}\n      </ListItemButton>\n    </ListItem>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Box\n        sx={{\n          p: 2,\n          borderBottom: `1px solid ${theme.palette.divider}`,\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n          color: 'white',\n        }}\n      >\n        <Typography variant=\"h6\" fontWeight={700}>\n          Fast Travel\n        </Typography>\n        <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n          Admin Dashboard\n        </Typography>\n      </Box>\n\n      {/* Connection Status */}\n      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <Box\n            sx={{\n              width: 8,\n              height: 8,\n              borderRadius: '50%',\n              backgroundColor: state.wsConnected\n                ? theme.palette.success.main\n                : theme.palette.error.main,\n            }}\n          />\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {state.wsConnected ? 'Real-time Connected' : 'Offline Mode'}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Menu Sections */}\n      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>\n        {menuSections.map((section, index) => (\n          <Box key={section.title}>\n            <Typography\n              variant=\"overline\"\n              sx={{\n                px: 2,\n                py: 1,\n                display: 'block',\n                color: theme.palette.text.secondary,\n                fontSize: '0.75rem',\n                fontWeight: 600,\n                letterSpacing: 1,\n              }}\n            >\n              {section.title}\n            </Typography>\n            <List dense sx={{ px: 0 }}>\n              {section.items.map(renderMenuItem)}\n            </List>\n            {index < menuSections.length - 1 && (\n              <Divider sx={{ mx: 2, my: 1 }} />\n            )}\n          </Box>\n        ))}\n      </Box>\n\n      {/* Footer */}\n      <Box\n        sx={{\n          p: 2,\n          borderTop: `1px solid ${theme.palette.divider}`,\n          backgroundColor: alpha(theme.palette.background.paper, 0.8),\n        }}\n      >\n        <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n          Version 1.0.0\n        </Typography>\n        <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n          Last updated: {new Date().toLocaleDateString()}\n        </Typography>\n      </Box>\n    </Box>\n  );\n\n  return (\n    <>\n      {/* Desktop Drawer */}\n      <Drawer\n        variant=\"persistent\"\n        anchor=\"left\"\n        open={state.sidebarOpen}\n        sx={{\n          display: { xs: 'none', md: 'block' },\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n            borderRight: `1px solid ${theme.palette.divider}`,\n            backgroundColor: theme.palette.background.paper,\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n\n      {/* Mobile Drawer */}\n      <Drawer\n        variant=\"temporary\"\n        anchor=\"left\"\n        open={state.sidebarOpen}\n        onClose={() => setSidebarOpen(false)}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile\n        }}\n        sx={{\n          display: { xs: 'block', md: 'none' },\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,eAAe,EACxBC,OAAO,IAAIC,SAAS,EACpBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,aAAa,EAC3BC,SAAS,IAAIC,SAAS,EACtBC,UAAU,IAAIC,YAAY,EAC1BC,MAAM,IAAIC,SAAS,EACnBC,aAAa,IAAIC,UAAU,EAC3BC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,UAAU,QACxB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,YAAY,GAAG,GAAG;AAgBxB,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,KAAK,GAAGpC,QAAQ,CAAC,CAAC;EACxB,MAAMqC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,KAAK;IAAEC;EAAe,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAEjD,MAAMa,mBAAmB,GAAIC,KAAa,IAAK;IAC7C,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAON,KAAK,CAACO,OAAO,CAACC,OAAO,CAACC,IAAI;MACnC,KAAK,OAAO;QACV,OAAOT,KAAK,CAACO,OAAO,CAACG,KAAK,CAACD,IAAI;MACjC,KAAK,SAAS;QACZ,OAAOT,KAAK,CAACO,OAAO,CAACI,OAAO,CAACF,IAAI;MACnC,KAAK,MAAM;QACT,OAAOT,KAAK,CAACO,OAAO,CAACK,IAAI,CAACH,IAAI;MAChC,KAAK,SAAS;QACZ,OAAOT,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI;MACnC,KAAK,WAAW;QACd,OAAOT,KAAK,CAACO,OAAO,CAACO,SAAS,CAACL,IAAI;MACrC;QACE,OAAOT,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI;IACrC;EACF,CAAC;EAED,MAAMM,YAAY,GAAIT,KAAc,IAAyE;IAC3G,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,WAAW;QACd,OAAO,WAAW;MACpB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMU,YAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,WAAW;MAClBC,IAAI,eAAE3B,OAAA,CAAC3B,aAAa;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAE3B,OAAA,CAACL,UAAU;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,SAAS;MACfpB,KAAK,EAAEH,KAAK,CAACwB,YAAY,CAACC,GAAG,KAAK,SAAS,GAAG,SAAS,GAAG;IAC5D,CAAC;EAEL,CAAC,EACD;IACEX,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,aAAa;MACjBC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAE3B,OAAA,CAACzB,eAAe;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,eAAE3B,OAAA,CAACvB,SAAS;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAE3B,OAAA,CAACrB,UAAU;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,WAAW;MACjBG,KAAK,EAAE1B,KAAK,CAAC2B,cAAc,CAACC;IAC9B,CAAC,EACD;MACEZ,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,eAAE3B,OAAA,CAACb,YAAY;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE,WAAW;MACjBpB,KAAK,EAAEH,KAAK,CAACwB,YAAY,CAACK,QAAQ,KAAK,WAAW,GAAG,SAAS,GAAG;IACnE,CAAC;EAEL,CAAC,EACD;IACEf,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAE3B,OAAA,CAACjB,aAAa;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,YAAY;MACnBC,IAAI,eAAE3B,OAAA,CAACf,SAAS;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAE3B,OAAA,CAACT,UAAU;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,SAAS;MACfG,KAAK,EAAE1B,KAAK,CAAC8B,MAAM,CAACC,MAAM,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,YAAY,CAAC,CAACL,MAAM;MAC/DzB,KAAK,EAAEH,KAAK,CAAC8B,MAAM,CAACI,IAAI,CAACF,KAAK,IAAIA,KAAK,CAACG,QAAQ,KAAK,UAAU,CAAC,GAAG,OAAO,GAAG;IAC/E,CAAC;EAEL,CAAC,EACD;IACErB,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAE3B,OAAA,CAACnB,YAAY;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAE3B,OAAA,CAACX,SAAS;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAE3B,OAAA,CAACP,UAAU;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,CACF;EAED,MAAMa,eAAe,GAAIb,IAAY,IAAK;IACxCxB,QAAQ,CAACwB,IAAI,CAAC;IACd,IAAIc,MAAM,CAACC,UAAU,GAAGzC,KAAK,CAAC0C,WAAW,CAACC,MAAM,CAACC,EAAE,EAAE;MACnDxC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyC,UAAU,GAAInB,IAAY,IAAK;IACnC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,OAAOzB,QAAQ,CAAC6C,QAAQ,KAAK,GAAG;IAClC;IACA,OAAO7C,QAAQ,CAAC6C,QAAQ,CAACC,UAAU,CAACrB,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMsB,cAAc,GAAIC,IAAc,iBACpCvD,OAAA,CAACtC,QAAQ;IAAe8F,cAAc;IAAAC,QAAA,eACpCzD,OAAA,CAACrC,cAAc;MACb+F,QAAQ,EAAEP,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAE;MAChC2B,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAACU,IAAI,CAACvB,IAAI,CAAE;MAC1C4B,EAAE,EAAE;QACFC,YAAY,EAAE,CAAC;QACfC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,GAAG;QACP,gBAAgB,EAAE;UAChBC,eAAe,EAAE7F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,EAAE,IAAI,CAAC;UACxD,SAAS,EAAE;YACTiD,eAAe,EAAE7F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,EAAE,IAAI;UACzD;QACF,CAAC;QACD,SAAS,EAAE;UACTiD,eAAe,EAAE7F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACoD,MAAM,CAACC,KAAK,EAAE,IAAI;QACzD;MACF,CAAE;MAAAT,QAAA,gBAEFzD,OAAA,CAACpC,YAAY;QACXgG,EAAE,EAAE;UACFhD,KAAK,EAAEuC,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAC,GACxB1B,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,GAC1BT,KAAK,CAACO,OAAO,CAACsD,IAAI,CAAC/C,SAAS;UAChCgD,QAAQ,EAAE;QACZ,CAAE;QAAAX,QAAA,EAEDF,IAAI,CAAC5B;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACf/B,OAAA,CAACnC,YAAY;QACXsD,OAAO,EAAEoC,IAAI,CAAC7B,KAAM;QACpBkC,EAAE,EAAE;UACF,4BAA4B,EAAE;YAC5BS,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAEnB,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;YAC7CpB,KAAK,EAAEuC,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAC,GACxB1B,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,GAC1BT,KAAK,CAACO,OAAO,CAACsD,IAAI,CAAChD;UACzB;QACF;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDwB,IAAI,CAACpB,KAAK,KAAKoC,SAAS,IAAIhB,IAAI,CAACpB,KAAK,GAAG,CAAC,iBACzCnC,OAAA,CAAC/B,IAAI;QACHyD,KAAK,EAAE6B,IAAI,CAACpB,KAAM;QAClBqC,IAAI,EAAC,OAAO;QACZ5D,KAAK,EAAES,YAAY,CAACkC,IAAI,CAAC3C,KAAK,CAAE;QAChCgD,EAAE,EAAE;UACFa,MAAM,EAAE,EAAE;UACVJ,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd;MAAE;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EACAwB,IAAI,CAAC3C,KAAK,IAAI2C,IAAI,CAACpB,KAAK,KAAKoC,SAAS,iBACrCvE,OAAA,CAAChC,GAAG;QACF4F,EAAE,EAAE;UACFc,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE,CAAC;UACTZ,YAAY,EAAE,KAAK;UACnBG,eAAe,EAAErD,mBAAmB,CAAC4C,IAAI,CAAC3C,KAAK;QACjD;MAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC,GA/DJwB,IAAI,CAAC9B,EAAE;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAgEZ,CACX;EAED,MAAM4C,aAAa,gBACjB3E,OAAA,CAAChC,GAAG;IAAC4F,EAAE,EAAE;MAAEa,MAAM,EAAE,MAAM;MAAEG,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAApB,QAAA,gBAEpEzD,OAAA,CAAChC,GAAG;MACF4F,EAAE,EAAE;QACFkB,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,aAAazE,KAAK,CAACO,OAAO,CAACmE,OAAO,EAAE;QAClDC,UAAU,EAAE,2BAA2B3E,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,QAAQT,KAAK,CAACO,OAAO,CAACM,OAAO,CAAC+D,IAAI,QAAQ;QAC3GtE,KAAK,EAAE;MACT,CAAE;MAAA6C,QAAA,gBAEFzD,OAAA,CAACjC,UAAU;QAACoH,OAAO,EAAC,IAAI;QAACb,UAAU,EAAE,GAAI;QAAAb,QAAA,EAAC;MAE1C;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/B,OAAA,CAACjC,UAAU;QAACoH,OAAO,EAAC,OAAO;QAACvB,EAAE,EAAE;UAAEwB,OAAO,EAAE;QAAI,CAAE;QAAA3B,QAAA,EAAC;MAElD;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN/B,OAAA,CAAChC,GAAG;MAAC4F,EAAE,EAAE;QAAEkB,CAAC,EAAE,CAAC;QAAEC,YAAY,EAAE,aAAazE,KAAK,CAACO,OAAO,CAACmE,OAAO;MAAG,CAAE;MAAAvB,QAAA,eACpEzD,OAAA,CAAChC,GAAG;QAAC4F,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACzDzD,OAAA,CAAChC,GAAG;UACF4F,EAAE,EAAE;YACFc,KAAK,EAAE,CAAC;YACRD,MAAM,EAAE,CAAC;YACTZ,YAAY,EAAE,KAAK;YACnBG,eAAe,EAAEvD,KAAK,CAAC8E,WAAW,GAC9BjF,KAAK,CAACO,OAAO,CAACC,OAAO,CAACC,IAAI,GAC1BT,KAAK,CAACO,OAAO,CAACG,KAAK,CAACD;UAC1B;QAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF/B,OAAA,CAACjC,UAAU;UAACoH,OAAO,EAAC,SAAS;UAACvE,KAAK,EAAC,gBAAgB;UAAA6C,QAAA,EACjDhD,KAAK,CAAC8E,WAAW,GAAG,qBAAqB,GAAG;QAAc;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA,CAAChC,GAAG;MAAC4F,EAAE,EAAE;QAAE4B,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,EAC3CnC,YAAY,CAACqE,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC/B7F,OAAA,CAAChC,GAAG;QAAAyF,QAAA,gBACFzD,OAAA,CAACjC,UAAU;UACToH,OAAO,EAAC,UAAU;UAClBvB,EAAE,EAAE;YACFkC,EAAE,EAAE,CAAC;YACLJ,EAAE,EAAE,CAAC;YACLd,OAAO,EAAE,OAAO;YAChBhE,KAAK,EAAEN,KAAK,CAACO,OAAO,CAACsD,IAAI,CAAC/C,SAAS;YACnCiD,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,GAAG;YACfyB,aAAa,EAAE;UACjB,CAAE;UAAAtC,QAAA,EAEDmC,OAAO,CAACrE;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACb/B,OAAA,CAACvC,IAAI;UAACuI,KAAK;UAACpC,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,EACvBmC,OAAO,CAACpE,KAAK,CAACmE,GAAG,CAACrC,cAAc;QAAC;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACN8D,KAAK,GAAGvE,YAAY,CAACe,MAAM,GAAG,CAAC,iBAC9BrC,OAAA,CAAClC,OAAO;UAAC8F,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEmC,EAAE,EAAE;UAAE;QAAE;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjC;MAAA,GApBO6D,OAAO,CAACrE,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBlB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN/B,OAAA,CAAChC,GAAG;MACF4F,EAAE,EAAE;QACFkB,CAAC,EAAE,CAAC;QACJoB,SAAS,EAAE,aAAa5F,KAAK,CAACO,OAAO,CAACmE,OAAO,EAAE;QAC/ChB,eAAe,EAAE7F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACoE,UAAU,CAACkB,KAAK,EAAE,GAAG;MAC5D,CAAE;MAAA1C,QAAA,gBAEFzD,OAAA,CAACjC,UAAU;QAACoH,OAAO,EAAC,SAAS;QAACvE,KAAK,EAAC,gBAAgB;QAACgE,OAAO,EAAC,OAAO;QAAAnB,QAAA,EAAC;MAErE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/B,OAAA,CAACjC,UAAU;QAACoH,OAAO,EAAC,SAAS;QAACvE,KAAK,EAAC,gBAAgB;QAACgE,OAAO,EAAC,OAAO;QAAAnB,QAAA,GAAC,gBACrD,EAAC,IAAI2C,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAAA;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE/B,OAAA,CAAAE,SAAA;IAAAuD,QAAA,gBAEEzD,OAAA,CAACxC,MAAM;MACL2H,OAAO,EAAC,YAAY;MACpBmB,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE9F,KAAK,CAAC+F,WAAY;MACxB5C,EAAE,EAAE;QACFgB,OAAO,EAAE;UAAE6B,EAAE,EAAE,MAAM;UAAEvD,EAAE,EAAE;QAAQ,CAAC;QACpC,oBAAoB,EAAE;UACpBwB,KAAK,EAAEvE,YAAY;UACnBuG,SAAS,EAAE,YAAY;UACvBC,WAAW,EAAE,aAAarG,KAAK,CAACO,OAAO,CAACmE,OAAO,EAAE;UACjDhB,eAAe,EAAE1D,KAAK,CAACO,OAAO,CAACoE,UAAU,CAACkB;QAC5C;MACF,CAAE;MAAA1C,QAAA,EAEDkB;IAAa;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGT/B,OAAA,CAACxC,MAAM;MACL2H,OAAO,EAAC,WAAW;MACnBmB,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE9F,KAAK,CAAC+F,WAAY;MACxBI,OAAO,EAAEA,CAAA,KAAMlG,cAAc,CAAC,KAAK,CAAE;MACrCmG,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;MACFlD,EAAE,EAAE;QACFgB,OAAO,EAAE;UAAE6B,EAAE,EAAE,OAAO;UAAEvD,EAAE,EAAE;QAAO,CAAC;QACpC,oBAAoB,EAAE;UACpBwB,KAAK,EAAEvE,YAAY;UACnBuG,SAAS,EAAE;QACb;MACF,CAAE;MAAAjD,QAAA,EAEDkB;IAAa;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAAC1B,EAAA,CA7VID,OAAiB;EAAA,QACPlC,QAAQ,EACL0B,WAAW,EACXC,WAAW,EACMC,aAAa;AAAA;AAAAiH,EAAA,GAJ3C3G,OAAiB;AA+VvB,eAAeA,OAAO;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}