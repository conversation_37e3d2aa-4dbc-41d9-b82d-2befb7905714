import requests
import json

def test_payment_endpoints():
    base_url = "http://localhost:8000"

    print("Testing Payment Endpoints...")
    print("=" * 40)

    # Test payment processing endpoint
    payment_data = {"booking_id": "test_booking_123"}

    try:
        print("Testing: POST /b2capis/payments/v1/")
        response = requests.post(f"{base_url}/b2capis/payments/v1/", json=payment_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        print()
    except Exception as e:
        print(f"Payment Processing Error: {e}")
        print()

    # Test payment callback endpoint
    try:
        print("Testing: POST /apis/b2capis/payments/callback/")
        response = requests.post(f"{base_url}/apis/b2capis/payments/callback/?payment=test_payment_123")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        print()
    except Exception as e:
        print(f"Payment Callback Error: {e}")
        print()

    # Test with empty body for callback
    try:
        print("Testing: POST /apis/b2capis/payments/callback/ (with body)")
        callback_data = {"payment": "test_payment_456"}
        response = requests.post(f"{base_url}/apis/b2capis/payments/callback/", json=callback_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        print()
    except Exception as e:
        print(f"Payment Callback (with body) Error: {e}")
        print()

if __name__ == "__main__":
    test_payment_endpoints()
