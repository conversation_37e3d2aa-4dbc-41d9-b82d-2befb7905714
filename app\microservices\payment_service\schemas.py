from pydantic import BaseModel
from typing import Optional, Any

class PaymentRequest(BaseModel):
    """
    Payment request schema that matches YAML specification exactly.
    """
    booking_id: Any  # Can be string or int based on YAML examples

class PaymentCallbackRequest(BaseModel):
    """
    Payment callback request schema.
    """
    payment: Optional[str] = None
    # Additional callback parameters can be added here

class PaymentResponse(BaseModel):
    """
    Payment response schema.
    """
    status: str
    message: str
    payment_id: Optional[str] = None
    transaction_id: Optional[str] = None
    booking_id: Optional[Any] = None
