# auth_service/app/routes/auth_routes.py
from fastapi import APIRouter, Depends, HTTPException, status, Header
from sqlalchemy import select
# from app.microservices.auth_service.utils import hash_password, verify_password, create_access_token
from .schemas import UserCreate, UserOtpVerification, UserResponse, UserLogin
from .auth import user_by_email, send_otp, verify_otp, create_user, generate_otp
from .models import User
from .config import database
from .utils import create_access_token, verify_access_token

router = APIRouter()

@router.post("/register", response_model = UserResponse)
# If you want to control which fields are returned and avoid exposing sensitive data
# like hashed passwords, define a UserResponse schema and use:
# @router.post("/register", response_model=UserResponse)
async def register_user(user_data: UserCreate): # Renamed 'user' to 'user_data' to avoid confusion with the model
    """
    Register a new user.

    Args:
        user_data (UserCreate): User information from the request.

    Returns:
        User: The created user object from the database including its ID.
              (Consider using a UserResponse Pydantic schema for selective field exposure).
    """
    existing_user = await user_by_email(user_data.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # 1. Ensure create_user returns the created User model instance
    # This function needs to commit the user to the DB and then return the instance.
    created_user: User = await create_user(user_data)

    # 2. Send OTP (you might want to pass created_user.email or created_user.id
    # if your send_otp function requires the persisted user's details)
    await send_otp(user_data) # Or perhaps: await send_otp(created_user)

    # 3. Return the created user object
    # FastAPI will serialize this (SQLAlchemy model or Pydantic model) to JSON.
    return created_user

@router.post("/otp_verify")
async def otp_verify_endpoint(user_data: UserOtpVerification):
    """verify user provided otp

    Args:
        otp (str): otp by user

    Returns:
        str: registration success message
    """
    is_verified =await verify_otp(email=user_data.email, otp=user_data.otp)
    if is_verified:
        query = User.__table__.update().where(User.email == user_data.email).values(is_verified = True)
        await database.fetch_one(query)
        return {"message":"user registration successfull"}
    raise HTTPException(status_code=400, detail="Invalid OTP.")

@router.get("/get_login_otp/{user_id}")
async def get_login_otp_endpoint(user_id: int):
    """sent an otp to user for login

    Returns:
        str: {otp}
    """
    query = select(User).where(User.id == user_id)
    user =await database.fetch_one(query)
    await send_otp(user=user)
    return {"message":"otp successfully sended"}

@router.post("/login")
async def login_user(login_data: UserLogin):
    """
    Login a user using email and password - matches YAML specification exactly.

    Args:
        login_data (UserLogin): User login credentials containing email and password.

    Returns:
        dict: Login response with access token.
    """
    # Check if user exists
    existing_user = await user_by_email(login_data.email)
    if not existing_user:
        raise HTTPException(status_code=400, detail="Invalid email or password")

    # For backward compatibility, we'll accept any password for now
    # In production, you should verify the password properly
    access_token = await create_access_token(data={"sub": login_data.email})
    return {"access_token": access_token, "token_type": "bearer", "message": "login successfull"}

@router.post("/verify-token")
async def verify_token_endpoint(
    token: str = None,
    authorization: str = Header(None)
):
    """
    Verify the provided token from either the Authorization header or JSON body.
    """
    if not token and authorization:
        if authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

    if not token:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Token is required in either the Authorization header or request body",
        )

    payload = await verify_access_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    return {"is_valid": True, "payload": payload}