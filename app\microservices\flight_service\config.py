from app.config import env
from databases import Database
from sqlalchemy import create_engine, MetaData


DATABASE_URL = env.get("FLIGHT_SERVICE_DATABASE_URL")
database = Database(DATABASE_URL)
metadata = MetaData()
engine = create_engine(DATABASE_URL, echo=True)

PROVIDER_CONFIG = {
    "tripjack": {
        "base_url": "https://apitest.tripjack.com/",
        "api_key": "6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9",
    },
    "provider2": {

    },
}

# Enhanced cache configuration with intelligent TTL management
cache_timer = {
	# Search results - longer TTL for better performance
	"FLIGHT_SEARCH": int(env.get("FLIGHT_SEARCH_CACHE_TIMER") or 900),  # 15 minutes
	"FLIGHT_SEARCH_POPULAR": int(env.get("FLIGHT_SEARCH_POPULAR_CACHE_TIMER") or 1800),  # 30 minutes for popular routes

	# Detail results - moderate TTL due to price volatility
	"FLIGHT_DETAIL": int(env.get("FLIGHT_DETAIL_CACHE_TIMER") or 300),  # 5 minutes
	"FLIGHT_DETAIL_PREMIUM": int(env.get("FLIGHT_DETAIL_PREMIUM_CACHE_TIMER") or 180),  # 3 minutes for premium fares

	# Booking related caches
	"BOOKING_DETAILS": int(env.get("BOOKING_DETAILS_CACHE_TIMER") or 3600),  # 1 hour
	"USER_BOOKINGS": int(env.get("USER_BOOKINGS_CACHE_TIMER") or 1800),  # 30 minutes

	# Static data with longer TTL
	"AIRPORT_DATA": int(env.get("AIRPORT_DATA_CACHE_TIMER") or 86400),  # 24 hours
	"AIRLINE_DATA": int(env.get("AIRLINE_DATA_CACHE_TIMER") or 43200),  # 12 hours

	# Memory cache TTL (shorter for hot data)
	"MEMORY_CACHE_TTL": int(env.get("MEMORY_CACHE_TTL") or 30),  # 30 seconds
}

# Cache warming configuration
cache_warming_config = {
	"enabled": env.get("CACHE_WARMING_ENABLED", "true").lower() == "true",
	"popular_routes_refresh_interval": int(env.get("POPULAR_ROUTES_REFRESH_INTERVAL") or 3600),  # 1 hour
	"max_routes_to_warm": int(env.get("MAX_ROUTES_TO_WARM") or 50),
	"warming_schedule": env.get("CACHE_WARMING_SCHEDULE", "0 */2 * * *"),  # Every 2 hours
}

# Performance monitoring configuration
performance_config = {
	"enable_metrics": env.get("ENABLE_PERFORMANCE_METRICS", "true").lower() == "true",
	"metrics_retention_days": int(env.get("METRICS_RETENTION_DAYS") or 7),
	"slow_query_threshold_ms": int(env.get("SLOW_QUERY_THRESHOLD_MS") or 1000),
	"cache_hit_rate_alert_threshold": float(env.get("CACHE_HIT_RATE_ALERT_THRESHOLD") or 0.8),
}
