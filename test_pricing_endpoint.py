import requests
import json

def test_pricing_endpoint():
    """Test the pricing endpoint with real flight data"""
    
    url = 'http://localhost:8000/apis/pricing'
    headers = {'Content-Type': 'application/json'}

    # Use a real flight index from the search results
    payload = {
        'Trips': [
            {
                'Amount': 5000.0,
                'Index': '30-6398088694_0DELBOMQP1131~959569102929226|125|TJ',
                'OrderID': 1,
                'TUI': 'flight_search:DEL-BOM:2025-06-25:4092630b6f123a1b'
            }
        ],
        'TripType': 'O'
    }

    print('Testing pricing endpoint with TripJack...')
    print(f'Payload: {json.dumps(payload, indent=2)}')
    print('-' * 50)

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=90)
        print(f'Status Code: {response.status_code}')
        result = response.json()
        print(f'Response keys: {list(result.keys())}')
        
        if 'Trips' in result and result['Trips']:
            print(f'✅ Found {len(result["Trips"])} trips in pricing response')
        else:
            print('❌ No trips found in pricing response')
        
        if 'Notices' in result and result['Notices']:
            print(f'📋 Notices: {result["Notices"]}')
            
        if 'NoFlightsMessage' in result:
            print(f'📋 No Flights Message: {result["NoFlightsMessage"]}')
            
        return result
        
    except requests.exceptions.Timeout:
        print('❌ Request timed out')
    except Exception as e:
        print(f'❌ Error: {e}')
        return None

if __name__ == "__main__":
    test_pricing_endpoint()
