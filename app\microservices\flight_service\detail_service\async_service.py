"""
Fully async flight detail service with advanced optimization features.
Implements async processing, request deduplication, circuit breaker, and intelligent caching.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from app.config import RedisCache
from app.microservices.flight_service.cache_service import generate_detail_cache_key
from app.microservices.flight_service.utils.async_provider_utils import (
    make_async_provider_call,
    CircuitBreakerOpenError,
    AsyncProviderError
)
from app.microservices.flight_service.utils.request_deduplication import (
    request_deduplicator,
    RequestExpiredError
)
from app.microservices.flight_service.detail_service.providers.tripjack.request_creator import (
    review_translate_client_to_provider
)
from app.microservices.flight_service.detail_service.providers.tripjack.response_converter import (
    review_translate_provider_to_client
)
from app.microservices.flight_service.detail_service.utils import get_detail_ids
from app.microservices.flight_service import config


class AsyncFlightDetail:
    """
    Fully async flight detail service with advanced optimization features.
    """
    
    def __init__(self):
        self.redis_client = RedisCache.connection()
        self.deduplicator = request_deduplicator
        
        # Performance tracking
        self.performance_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "provider_calls": 0,
            "deduplicated_requests": 0,
            "circuit_breaker_blocks": 0,
            "errors": 0
        }
    
    async def detail(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced async flight detail retrieval with all optimization features.
        
        Args:
            request_data: Detail request parameters
            
        Returns:
            Flight detail results with performance metadata
        """
        start_time = time.time()
        self.performance_stats["total_requests"] += 1
        
        try:
            # Extract fare ID from the request data
            fare_id = get_detail_ids(request_data)
            
            # Generate cache key and request hash for deduplication
            cache_key = generate_detail_cache_key(fare_id, request_data)
            request_hash = self.deduplicator.generate_request_hash(
                {**request_data, "fare_id": fare_id}, 
                "flight_detail"
            )
            
            # Generate TUI (Transaction Unique Identifier)
            tui = f"{fare_id}|TJ|detail"
            context = {"fare_id": fare_id, "TUI": tui}
            
            # Step 1: Check cache first
            cached_results = await self._check_cache(cache_key, tui)
            if cached_results:
                self.performance_stats["cache_hits"] += 1
                
                # Add performance metadata
                response_time = time.time() - start_time
                cached_results.update({
                    "TUI": tui,
                    "cache_hit": True,
                    "response_time_ms": round(response_time * 1000, 2),
                    "data_source": "cache",
                    "request_hash": request_hash
                })
                
                return cached_results
            
            # Step 2: Use request deduplication for provider calls
            try:
                result = await self.deduplicator.deduplicate_request(
                    request_hash,
                    lambda: self._fetch_from_provider(
                        request_data, context, cache_key, start_time
                    )
                )
                
                # Check if this was a deduplicated request
                if result.get("deduplicated", False):
                    self.performance_stats["deduplicated_requests"] += 1
                
                return result
                
            except RequestExpiredError:
                # Handle expired deduplicated request
                return await self._create_background_response(
                    request_data, context, start_time
                )
            
        except Exception as e:
            self.performance_stats["errors"] += 1
            print(f"Async detail error: {str(e)}")
            return await self._create_error_response(request_data, str(e), start_time)
    
    async def _check_cache(self, cache_key: str, tui: str) -> Optional[Dict[str, Any]]:
        """
        Check cache for existing detail results.
        
        Args:
            cache_key: Cache key to check
            tui: Transaction unique identifier
            
        Returns:
            Cached results if found, None otherwise
        """
        try:
            # Check both the new cache key and legacy TUI
            cached_results = self.redis_client.get_cache(cache_key)
            if not cached_results:
                cached_results = self.redis_client.get_cache(tui)
            
            if cached_results:
                # Trigger background refresh if data is getting stale
                await self._trigger_background_refresh_if_needed(
                    cached_results, cache_key, tui
                )
                return cached_results
            
            return None
            
        except Exception as e:
            print(f"Detail cache check error: {str(e)}")
            return None
    
    async def _fetch_from_provider(
        self, 
        request_data: Dict[str, Any], 
        context: Dict[str, Any],
        cache_key: str, 
        start_time: float
    ) -> Dict[str, Any]:
        """
        Fetch flight detail data from provider with circuit breaker protection.
        
        Args:
            request_data: Detail request data
            context: Request context with fare_id and TUI
            cache_key: Cache key for storing results
            start_time: Request start time
            
        Returns:
            Provider response data
        """
        try:
            self.performance_stats["provider_calls"] += 1
            
            # Translate request for provider
            provider_payload = review_translate_client_to_provider(context, request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/review"
            
            # Make async provider call with circuit breaker protection
            provider_response = await make_async_provider_call(
                provider_api_url,
                provider_payload,
                "tripjack"
            )
            
            # Translate response
            client_response = review_translate_provider_to_client(
                request_data, provider_response, context
            )
            
            # Add metadata
            response_time = time.time() - start_time
            client_response.update({
                "TUI": context["TUI"],
                "cache_hit": False,
                "response_time_ms": round(response_time * 1000, 2),
                "data_source": "provider_async",
                "cached_at": datetime.now(timezone.utc).isoformat(),
                "fare_id": context["fare_id"]
            })
            
            # Cache the results asynchronously
            asyncio.create_task(self._cache_results(cache_key, context["TUI"], client_response))
            
            return client_response
            
        except CircuitBreakerOpenError:
            self.performance_stats["circuit_breaker_blocks"] += 1
            print("Circuit breaker is open for detail service, falling back to background task")
            return await self._create_background_response(request_data, context, start_time)
            
        except AsyncProviderError as e:
            print(f"Detail provider error: {str(e)}")
            return await self._create_background_response(request_data, context, start_time)
    
    async def _cache_results(
        self, 
        cache_key: str, 
        tui: str, 
        results: Dict[str, Any]
    ):
        """
        Cache results asynchronously using both new and legacy keys.
        
        Args:
            cache_key: New cache key
            tui: Legacy TUI key
            results: Results to cache
        """
        try:
            ttl = config.cache_timer.get('FLIGHT_DETAIL', 300)  # 5 minutes
            
            # Cache with both keys for backward compatibility
            self.redis_client.set_cache(cache_key, results, ttl)
            self.redis_client.set_cache(tui, results, ttl)
            
        except Exception as e:
            print(f"Async detail cache write error: {str(e)}")
    
    async def _trigger_background_refresh_if_needed(
        self, 
        cached_results: Dict[str, Any], 
        cache_key: str,
        tui: str
    ):
        """
        Trigger background refresh if cached data is getting stale.
        
        Args:
            cached_results: Current cached results
            cache_key: Cache key
            tui: Transaction unique identifier
        """
        try:
            cached_at_str = cached_results.get("cached_at")
            if cached_at_str:
                cached_at = datetime.fromisoformat(cached_at_str.replace('Z', '+00:00'))
                age_minutes = (datetime.now(timezone.utc) - cached_at).total_seconds() / 60
                
                # Refresh if data is older than 3 minutes (details are more volatile)
                if age_minutes > 3:
                    # Import here to avoid circular imports
                    from app.microservices.flight_service.detail_service.tasks import fetch_flight_detail_task
                    
                    # Extract context from cached results
                    context = {
                        "fare_id": cached_results.get("fare_id", ""),
                        "TUI": tui
                    }
                    
                    # Extract original request data (simplified)
                    request_data = self._extract_request_from_cached_results(cached_results)
                    
                    fetch_flight_detail_task.delay(
                        "RefreshProvider", 
                        request_data, 
                        tui, 
                        context, 
                        context["fare_id"]
                    )
                    
        except Exception as e:
            print(f"Detail background refresh trigger error: {str(e)}")
    
    def _extract_request_from_cached_results(self, cached_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract basic request data from cached results for background refresh.
        
        Args:
            cached_results: Cached detail results
            
        Returns:
            Basic request data
        """
        try:
            # Try to extract passenger counts and other data from cached results
            # This is a simplified extraction - in practice, you might store
            # the original request data in the cache
            return {
                "ADT": 1,  # Default values
                "CHD": 0,
                "INF": 0
            }
        except Exception as e:
            print(f"Error extracting request from cached results: {str(e)}")
            return {"ADT": 1, "CHD": 0, "INF": 0}
    
    async def _create_background_response(
        self, 
        request_data: Dict[str, Any], 
        context: Dict[str, Any],
        start_time: float
    ) -> Dict[str, Any]:
        """
        Create response for background processing.
        
        Args:
            request_data: Original request data
            context: Request context
            start_time: Request start time
            
        Returns:
            Background processing response
        """
        # Import here to avoid circular imports
        from app.microservices.flight_service.detail_service.tasks import fetch_flight_detail_task
        
        # Trigger background task
        fetch_flight_detail_task.delay(
            "BackgroundProvider", 
            request_data, 
            context["TUI"], 
            context, 
            context["fare_id"]
        )
        
        return {
            "TUI": context["TUI"],
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": ["Detail processing in progress. Please check back in a few moments."],
            "Trips": None,
            "Code": "202",
            "Msg": ["Flight detail processing initiated. Results will be available shortly."],
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "background_task",
            "processing_status": "in_progress"
        }
    
    async def _create_error_response(
        self, 
        request_data: Dict[str, Any], 
        error_message: str, 
        start_time: float
    ) -> Dict[str, Any]:
        """
        Create standardized error response.
        
        Args:
            request_data: Original request data
            error_message: Error message
            start_time: Request start time
            
        Returns:
            Error response
        """
        return {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Flight detail service error"],
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "error",
            "error_details": error_message
        }
    
    async def get_detail(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced async retrieval of cached flight details.
        
        Args:
            request_data: Request data containing TUI
            
        Returns:
            Cached detail results
        """
        tui = request_data.get('TUI')
        
        if not tui:
            return await self._create_error_response(
                request_data, 
                "Missing TUI parameter", 
                time.time()
            )
        
        try:
            # Attempt to retrieve results from cache
            results = self.redis_client.get_cache(tui)
            
            if not results:
                return {
                    "TUI": tui,
                    "Completed": False,
                    "CeilingInfo": None,
                    "CurrencyCode": "INR",
                    "Notices": ["Flight details have expired. Please request fresh pricing."],
                    "Trips": None,
                    "Code": "404",
                    "Msg": ["Flight details not found or expired"]
                }
            
            # Add metadata and return results
            results.update({
                "TUI": tui,
                "retrieved_at": datetime.now(timezone.utc).isoformat(),
                "data_source": "cache_retrieval"
            })
            
            return results
            
        except Exception as e:
            print(f"Async get detail error: {str(e)}")
            return await self._create_error_response(request_data, str(e), time.time())
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_requests = self.performance_stats["total_requests"]
        
        return {
            **self.performance_stats,
            "cache_hit_rate": (
                self.performance_stats["cache_hits"] / total_requests 
                if total_requests > 0 else 0
            ),
            "deduplication_rate": (
                self.performance_stats["deduplicated_requests"] / total_requests 
                if total_requests > 0 else 0
            ),
            "error_rate": (
                self.performance_stats["errors"] / total_requests 
                if total_requests > 0 else 0
            )
        }


# Global async flight detail instance
async_flight_detail = AsyncFlightDetail()
