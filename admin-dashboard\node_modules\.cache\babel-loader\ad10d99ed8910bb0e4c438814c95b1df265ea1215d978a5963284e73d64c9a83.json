{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PROJECTS\\\\fast_travel_backend\\\\admin-dashboard\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Divider, Typography, Box, Chip, useTheme, alpha } from '@mui/material';\nimport { Dashboard as DashboardIcon, Speed as PerformanceIcon, Storage as CacheIcon, Search as SearchIcon, Settings as SettingsIcon, Assessment as AnalyticsIcon, BugReport as ErrorIcon, CloudQueue as TripJackIcon, People as UsersIcon, Notifications as AlertsIcon, GetApp as ExportIcon, HealthAndSafety as HealthIcon } from '@mui/icons-material';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAppContext } from '../../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DRAWER_WIDTH = 280;\nconst Sidebar = () => {\n  _s();\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    state,\n    setSidebarOpen\n  } = useAppContext();\n  const getColorFromPalette = color => {\n    switch (color) {\n      case 'success':\n        return theme.palette.success.main;\n      case 'error':\n        return theme.palette.error.main;\n      case 'warning':\n        return theme.palette.warning.main;\n      case 'info':\n        return theme.palette.info.main;\n      case 'primary':\n        return theme.palette.primary.main;\n      case 'secondary':\n        return theme.palette.secondary.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n  const menuSections = [{\n    title: 'Overview',\n    items: [{\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this),\n      path: '/'\n    }, {\n      id: 'health',\n      label: 'System Health',\n      icon: /*#__PURE__*/_jsxDEV(HealthIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 17\n      }, this),\n      path: '/health',\n      color: state.systemHealth.api === 'healthy' ? 'success' : 'error'\n    }]\n  }, {\n    title: 'Monitoring',\n    items: [{\n      id: 'performance',\n      label: 'Performance',\n      icon: /*#__PURE__*/_jsxDEV(PerformanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this),\n      path: '/performance'\n    }, {\n      id: 'cache',\n      label: 'Cache Management',\n      icon: /*#__PURE__*/_jsxDEV(CacheIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this),\n      path: '/cache'\n    }, {\n      id: 'searches',\n      label: 'Flight Searches',\n      icon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 17\n      }, this),\n      path: '/searches',\n      badge: state.activeSearches.length\n    }, {\n      id: 'tripjack',\n      label: 'TripJack Integration',\n      icon: /*#__PURE__*/_jsxDEV(TripJackIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this),\n      path: '/tripjack',\n      color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning'\n    }]\n  }, {\n    title: 'Analytics',\n    items: [{\n      id: 'analytics',\n      label: 'Route Analytics',\n      icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this),\n      path: '/analytics'\n    }, {\n      id: 'errors',\n      label: 'Error Logs',\n      icon: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 17\n      }, this),\n      path: '/errors'\n    }, {\n      id: 'alerts',\n      label: 'System Alerts',\n      icon: /*#__PURE__*/_jsxDEV(AlertsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 17\n      }, this),\n      path: '/alerts',\n      badge: state.alerts.filter(alert => !alert.acknowledged).length,\n      color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning'\n    }]\n  }, {\n    title: 'Administration',\n    items: [{\n      id: 'settings',\n      label: 'Configuration',\n      icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 17\n      }, this),\n      path: '/settings'\n    }, {\n      id: 'users',\n      label: 'User Management',\n      icon: /*#__PURE__*/_jsxDEV(UsersIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 17\n      }, this),\n      path: '/users'\n    }, {\n      id: 'export',\n      label: 'Data Export',\n      icon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 17\n      }, this),\n      path: '/export'\n    }]\n  }];\n  const handleItemClick = path => {\n    navigate(path);\n    if (window.innerWidth < theme.breakpoints.values.md) {\n      setSidebarOpen(false);\n    }\n  };\n  const isSelected = path => {\n    if (path === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuItem = item => /*#__PURE__*/_jsxDEV(ListItem, {\n    disablePadding: true,\n    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isSelected(item.path),\n      onClick: () => handleItemClick(item.path),\n      sx: {\n        borderRadius: 1,\n        mx: 1,\n        mb: 0.5,\n        '&.Mui-selected': {\n          backgroundColor: alpha(theme.palette.primary.main, 0.12),\n          '&:hover': {\n            backgroundColor: alpha(theme.palette.primary.main, 0.16)\n          }\n        },\n        '&:hover': {\n          backgroundColor: alpha(theme.palette.action.hover, 0.08)\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        sx: {\n          color: isSelected(item.path) ? theme.palette.primary.main : theme.palette.text.secondary,\n          minWidth: 40\n        },\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: item.label,\n        sx: {\n          '& .MuiListItemText-primary': {\n            fontSize: '0.875rem',\n            fontWeight: isSelected(item.path) ? 600 : 400,\n            color: isSelected(item.path) ? theme.palette.primary.main : theme.palette.text.primary\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), item.badge !== undefined && item.badge > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n        label: item.badge,\n        size: \"small\",\n        color: item.color || 'primary',\n        sx: {\n          height: 20,\n          fontSize: '0.75rem',\n          fontWeight: 600\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), item.color && item.badge === undefined && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          backgroundColor: getColorFromPalette(item.color)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)\n  }, item.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: `1px solid ${theme.palette.divider}`,\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: 700,\n        children: \"Fast Travel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          opacity: 0.9\n        },\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: `1px solid ${theme.palette.divider}`\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 8,\n            height: 8,\n            borderRadius: '50%',\n            backgroundColor: state.wsConnected ? theme.palette.success.main : theme.palette.error.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: state.wsConnected ? 'Real-time Connected' : 'Offline Mode'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflow: 'auto',\n        py: 1\n      },\n      children: menuSections.map((section, index) => /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"overline\",\n          sx: {\n            px: 2,\n            py: 1,\n            display: 'block',\n            color: theme.palette.text.secondary,\n            fontSize: '0.75rem',\n            fontWeight: 600,\n            letterSpacing: 1\n          },\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          sx: {\n            px: 0\n          },\n          children: section.items.map(renderMenuItem)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this), index < menuSections.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mx: 2,\n            my: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 15\n        }, this)]\n      }, section.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderTop: `1px solid ${theme.palette.divider}`,\n        backgroundColor: alpha(theme.palette.background.paper, 0.8)\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        display: \"block\",\n        children: \"Version 1.0.0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        display: \"block\",\n        children: [\"Last updated: \", new Date().toLocaleDateString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"persistent\",\n      anchor: \"left\",\n      open: state.sidebarOpen,\n      sx: {\n        display: {\n          xs: 'none',\n          md: 'block'\n        },\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box',\n          borderRight: `1px solid ${theme.palette.divider}`,\n          backgroundColor: theme.palette.background.paper\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"temporary\",\n      anchor: \"left\",\n      open: state.sidebarOpen,\n      onClose: () => setSidebarOpen(false),\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile\n      },\n      sx: {\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box'\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"eo3I+AzjG+Wp98ovqyCajcgBx50=\", false, function () {\n  return [useTheme, useLocation, useNavigate, useAppContext];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Typography", "Box", "Chip", "useTheme", "alpha", "Dashboard", "DashboardIcon", "Speed", "PerformanceIcon", "Storage", "CacheIcon", "Search", "SearchIcon", "Settings", "SettingsIcon", "Assessment", "AnalyticsIcon", "BugReport", "ErrorIcon", "CloudQueue", "TripJackIcon", "People", "UsersIcon", "Notifications", "AlertsIcon", "GetApp", "ExportIcon", "HealthAndSafety", "HealthIcon", "useLocation", "useNavigate", "useAppContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DRAWER_WIDTH", "Sidebar", "_s", "theme", "location", "navigate", "state", "setSidebarOpen", "getColorFromPalette", "color", "palette", "success", "main", "error", "warning", "info", "primary", "secondary", "menuSections", "title", "items", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "systemHealth", "api", "badge", "activeSearches", "length", "tripjack", "alerts", "filter", "alert", "acknowledged", "some", "severity", "handleItemClick", "window", "innerWidth", "breakpoints", "values", "md", "isSelected", "pathname", "startsWith", "renderMenuItem", "item", "disablePadding", "children", "selected", "onClick", "sx", "borderRadius", "mx", "mb", "backgroundColor", "action", "hover", "text", "min<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "undefined", "size", "height", "width", "drawerContent", "display", "flexDirection", "p", "borderBottom", "divider", "background", "dark", "variant", "opacity", "alignItems", "gap", "wsConnected", "flex", "overflow", "py", "map", "section", "index", "px", "letterSpacing", "dense", "my", "borderTop", "paper", "Date", "toLocaleDateString", "anchor", "open", "sidebarOpen", "xs", "boxSizing", "borderRight", "onClose", "ModalProps", "keepMounted", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PROJECTS/fast_travel_backend/admin-dashboard/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Typography,\n  Box,\n  Chip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  Speed as PerformanceIcon,\n  Storage as CacheIcon,\n  Search as SearchIcon,\n  Settings as SettingsIcon,\n  Assessment as AnalyticsIcon,\n  BugReport as ErrorIcon,\n  CloudQueue as TripJackIcon,\n  People as UsersIcon,\n  Notifications as AlertsIcon,\n  GetApp as ExportIcon,\n  HealthAndSafety as HealthIcon,\n} from '@mui/icons-material';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAppContext } from '../../contexts/AppContext';\n\nconst DRAWER_WIDTH = 280;\n\ninterface MenuItem {\n  id: string;\n  label: string;\n  icon: React.ReactElement;\n  path: string;\n  badge?: number;\n  color?: string;\n}\n\ninterface MenuSection {\n  title: string;\n  items: MenuItem[];\n}\n\nconst Sidebar: React.FC = () => {\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { state, setSidebarOpen } = useAppContext();\n\n  const getColorFromPalette = (color: string) => {\n    switch (color) {\n      case 'success':\n        return theme.palette.success.main;\n      case 'error':\n        return theme.palette.error.main;\n      case 'warning':\n        return theme.palette.warning.main;\n      case 'info':\n        return theme.palette.info.main;\n      case 'primary':\n        return theme.palette.primary.main;\n      case 'secondary':\n        return theme.palette.secondary.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n\n  const menuSections: MenuSection[] = [\n    {\n      title: 'Overview',\n      items: [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: <DashboardIcon />,\n          path: '/',\n        },\n        {\n          id: 'health',\n          label: 'System Health',\n          icon: <HealthIcon />,\n          path: '/health',\n          color: state.systemHealth.api === 'healthy' ? 'success' : 'error',\n        },\n      ],\n    },\n    {\n      title: 'Monitoring',\n      items: [\n        {\n          id: 'performance',\n          label: 'Performance',\n          icon: <PerformanceIcon />,\n          path: '/performance',\n        },\n        {\n          id: 'cache',\n          label: 'Cache Management',\n          icon: <CacheIcon />,\n          path: '/cache',\n        },\n        {\n          id: 'searches',\n          label: 'Flight Searches',\n          icon: <SearchIcon />,\n          path: '/searches',\n          badge: state.activeSearches.length,\n        },\n        {\n          id: 'tripjack',\n          label: 'TripJack Integration',\n          icon: <TripJackIcon />,\n          path: '/tripjack',\n          color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning',\n        },\n      ],\n    },\n    {\n      title: 'Analytics',\n      items: [\n        {\n          id: 'analytics',\n          label: 'Route Analytics',\n          icon: <AnalyticsIcon />,\n          path: '/analytics',\n        },\n        {\n          id: 'errors',\n          label: 'Error Logs',\n          icon: <ErrorIcon />,\n          path: '/errors',\n        },\n        {\n          id: 'alerts',\n          label: 'System Alerts',\n          icon: <AlertsIcon />,\n          path: '/alerts',\n          badge: state.alerts.filter(alert => !alert.acknowledged).length,\n          color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning',\n        },\n      ],\n    },\n    {\n      title: 'Administration',\n      items: [\n        {\n          id: 'settings',\n          label: 'Configuration',\n          icon: <SettingsIcon />,\n          path: '/settings',\n        },\n        {\n          id: 'users',\n          label: 'User Management',\n          icon: <UsersIcon />,\n          path: '/users',\n        },\n        {\n          id: 'export',\n          label: 'Data Export',\n          icon: <ExportIcon />,\n          path: '/export',\n        },\n      ],\n    },\n  ];\n\n  const handleItemClick = (path: string) => {\n    navigate(path);\n    if (window.innerWidth < theme.breakpoints.values.md) {\n      setSidebarOpen(false);\n    }\n  };\n\n  const isSelected = (path: string) => {\n    if (path === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuItem = (item: MenuItem) => (\n    <ListItem key={item.id} disablePadding>\n      <ListItemButton\n        selected={isSelected(item.path)}\n        onClick={() => handleItemClick(item.path)}\n        sx={{\n          borderRadius: 1,\n          mx: 1,\n          mb: 0.5,\n          '&.Mui-selected': {\n            backgroundColor: alpha(theme.palette.primary.main, 0.12),\n            '&:hover': {\n              backgroundColor: alpha(theme.palette.primary.main, 0.16),\n            },\n          },\n          '&:hover': {\n            backgroundColor: alpha(theme.palette.action.hover, 0.08),\n          },\n        }}\n      >\n        <ListItemIcon\n          sx={{\n            color: isSelected(item.path)\n              ? theme.palette.primary.main\n              : theme.palette.text.secondary,\n            minWidth: 40,\n          }}\n        >\n          {item.icon}\n        </ListItemIcon>\n        <ListItemText\n          primary={item.label}\n          sx={{\n            '& .MuiListItemText-primary': {\n              fontSize: '0.875rem',\n              fontWeight: isSelected(item.path) ? 600 : 400,\n              color: isSelected(item.path)\n                ? theme.palette.primary.main\n                : theme.palette.text.primary,\n            },\n          }}\n        />\n        {item.badge !== undefined && item.badge > 0 && (\n          <Chip\n            label={item.badge}\n            size=\"small\"\n            color={item.color as any || 'primary'}\n            sx={{\n              height: 20,\n              fontSize: '0.75rem',\n              fontWeight: 600,\n            }}\n          />\n        )}\n        {item.color && item.badge === undefined && (\n          <Box\n            sx={{\n              width: 8,\n              height: 8,\n              borderRadius: '50%',\n              backgroundColor: getColorFromPalette(item.color),\n            }}\n          />\n        )}\n      </ListItemButton>\n    </ListItem>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Box\n        sx={{\n          p: 2,\n          borderBottom: `1px solid ${theme.palette.divider}`,\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n          color: 'white',\n        }}\n      >\n        <Typography variant=\"h6\" fontWeight={700}>\n          Fast Travel\n        </Typography>\n        <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n          Admin Dashboard\n        </Typography>\n      </Box>\n\n      {/* Connection Status */}\n      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <Box\n            sx={{\n              width: 8,\n              height: 8,\n              borderRadius: '50%',\n              backgroundColor: state.wsConnected\n                ? theme.palette.success.main\n                : theme.palette.error.main,\n            }}\n          />\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {state.wsConnected ? 'Real-time Connected' : 'Offline Mode'}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Menu Sections */}\n      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>\n        {menuSections.map((section, index) => (\n          <Box key={section.title}>\n            <Typography\n              variant=\"overline\"\n              sx={{\n                px: 2,\n                py: 1,\n                display: 'block',\n                color: theme.palette.text.secondary,\n                fontSize: '0.75rem',\n                fontWeight: 600,\n                letterSpacing: 1,\n              }}\n            >\n              {section.title}\n            </Typography>\n            <List dense sx={{ px: 0 }}>\n              {section.items.map(renderMenuItem)}\n            </List>\n            {index < menuSections.length - 1 && (\n              <Divider sx={{ mx: 2, my: 1 }} />\n            )}\n          </Box>\n        ))}\n      </Box>\n\n      {/* Footer */}\n      <Box\n        sx={{\n          p: 2,\n          borderTop: `1px solid ${theme.palette.divider}`,\n          backgroundColor: alpha(theme.palette.background.paper, 0.8),\n        }}\n      >\n        <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n          Version 1.0.0\n        </Typography>\n        <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n          Last updated: {new Date().toLocaleDateString()}\n        </Typography>\n      </Box>\n    </Box>\n  );\n\n  return (\n    <>\n      {/* Desktop Drawer */}\n      <Drawer\n        variant=\"persistent\"\n        anchor=\"left\"\n        open={state.sidebarOpen}\n        sx={{\n          display: { xs: 'none', md: 'block' },\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n            borderRight: `1px solid ${theme.palette.divider}`,\n            backgroundColor: theme.palette.background.paper,\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n\n      {/* Mobile Drawer */}\n      <Drawer\n        variant=\"temporary\"\n        anchor=\"left\"\n        open={state.sidebarOpen}\n        onClose={() => setSidebarOpen(false)}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile\n        }}\n        sx={{\n          display: { xs: 'block', md: 'none' },\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,eAAe,EACxBC,OAAO,IAAIC,SAAS,EACpBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,aAAa,EAC3BC,SAAS,IAAIC,SAAS,EACtBC,UAAU,IAAIC,YAAY,EAC1BC,MAAM,IAAIC,SAAS,EACnBC,aAAa,IAAIC,UAAU,EAC3BC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,UAAU,QACxB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,YAAY,GAAG,GAAG;AAgBxB,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,KAAK,GAAGpC,QAAQ,CAAC,CAAC;EACxB,MAAMqC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,KAAK;IAAEC;EAAe,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAEjD,MAAMa,mBAAmB,GAAIC,KAAa,IAAK;IAC7C,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAON,KAAK,CAACO,OAAO,CAACC,OAAO,CAACC,IAAI;MACnC,KAAK,OAAO;QACV,OAAOT,KAAK,CAACO,OAAO,CAACG,KAAK,CAACD,IAAI;MACjC,KAAK,SAAS;QACZ,OAAOT,KAAK,CAACO,OAAO,CAACI,OAAO,CAACF,IAAI;MACnC,KAAK,MAAM;QACT,OAAOT,KAAK,CAACO,OAAO,CAACK,IAAI,CAACH,IAAI;MAChC,KAAK,SAAS;QACZ,OAAOT,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI;MACnC,KAAK,WAAW;QACd,OAAOT,KAAK,CAACO,OAAO,CAACO,SAAS,CAACL,IAAI;MACrC;QACE,OAAOT,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI;IACrC;EACF,CAAC;EAED,MAAMM,YAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,WAAW;MAClBC,IAAI,eAAE1B,OAAA,CAAC3B,aAAa;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAE1B,OAAA,CAACL,UAAU;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,SAAS;MACfnB,KAAK,EAAEH,KAAK,CAACuB,YAAY,CAACC,GAAG,KAAK,SAAS,GAAG,SAAS,GAAG;IAC5D,CAAC;EAEL,CAAC,EACD;IACEX,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,aAAa;MACjBC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAE1B,OAAA,CAACzB,eAAe;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,eAAE1B,OAAA,CAACvB,SAAS;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAE1B,OAAA,CAACrB,UAAU;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,WAAW;MACjBG,KAAK,EAAEzB,KAAK,CAAC0B,cAAc,CAACC;IAC9B,CAAC,EACD;MACEZ,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,eAAE1B,OAAA,CAACb,YAAY;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE,WAAW;MACjBnB,KAAK,EAAEH,KAAK,CAACuB,YAAY,CAACK,QAAQ,KAAK,WAAW,GAAG,SAAS,GAAG;IACnE,CAAC;EAEL,CAAC,EACD;IACEf,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAE1B,OAAA,CAACjB,aAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,YAAY;MACnBC,IAAI,eAAE1B,OAAA,CAACf,SAAS;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAE1B,OAAA,CAACT,UAAU;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,SAAS;MACfG,KAAK,EAAEzB,KAAK,CAAC6B,MAAM,CAACC,MAAM,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,YAAY,CAAC,CAACL,MAAM;MAC/DxB,KAAK,EAAEH,KAAK,CAAC6B,MAAM,CAACI,IAAI,CAACF,KAAK,IAAIA,KAAK,CAACG,QAAQ,KAAK,UAAU,CAAC,GAAG,OAAO,GAAG;IAC/E,CAAC;EAEL,CAAC,EACD;IACErB,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAE1B,OAAA,CAACnB,YAAY;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAE1B,OAAA,CAACX,SAAS;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAE1B,OAAA,CAACP,UAAU;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,CACF;EAED,MAAMa,eAAe,GAAIb,IAAY,IAAK;IACxCvB,QAAQ,CAACuB,IAAI,CAAC;IACd,IAAIc,MAAM,CAACC,UAAU,GAAGxC,KAAK,CAACyC,WAAW,CAACC,MAAM,CAACC,EAAE,EAAE;MACnDvC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMwC,UAAU,GAAInB,IAAY,IAAK;IACnC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,OAAOxB,QAAQ,CAAC4C,QAAQ,KAAK,GAAG;IAClC;IACA,OAAO5C,QAAQ,CAAC4C,QAAQ,CAACC,UAAU,CAACrB,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMsB,cAAc,GAAIC,IAAc,iBACpCtD,OAAA,CAACtC,QAAQ;IAAe6F,cAAc;IAAAC,QAAA,eACpCxD,OAAA,CAACrC,cAAc;MACb8F,QAAQ,EAAEP,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAE;MAChC2B,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAACU,IAAI,CAACvB,IAAI,CAAE;MAC1C4B,EAAE,EAAE;QACFC,YAAY,EAAE,CAAC;QACfC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,GAAG;QACP,gBAAgB,EAAE;UAChBC,eAAe,EAAE5F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,EAAE,IAAI,CAAC;UACxD,SAAS,EAAE;YACTgD,eAAe,EAAE5F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,EAAE,IAAI;UACzD;QACF,CAAC;QACD,SAAS,EAAE;UACTgD,eAAe,EAAE5F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACmD,MAAM,CAACC,KAAK,EAAE,IAAI;QACzD;MACF,CAAE;MAAAT,QAAA,gBAEFxD,OAAA,CAACpC,YAAY;QACX+F,EAAE,EAAE;UACF/C,KAAK,EAAEsC,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAC,GACxBzB,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,GAC1BT,KAAK,CAACO,OAAO,CAACqD,IAAI,CAAC9C,SAAS;UAChC+C,QAAQ,EAAE;QACZ,CAAE;QAAAX,QAAA,EAEDF,IAAI,CAAC5B;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACf9B,OAAA,CAACnC,YAAY;QACXsD,OAAO,EAAEmC,IAAI,CAAC7B,KAAM;QACpBkC,EAAE,EAAE;UACF,4BAA4B,EAAE;YAC5BS,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAEnB,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;YAC7CnB,KAAK,EAAEsC,UAAU,CAACI,IAAI,CAACvB,IAAI,CAAC,GACxBzB,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,GAC1BT,KAAK,CAACO,OAAO,CAACqD,IAAI,CAAC/C;UACzB;QACF;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDwB,IAAI,CAACpB,KAAK,KAAKoC,SAAS,IAAIhB,IAAI,CAACpB,KAAK,GAAG,CAAC,iBACzClC,OAAA,CAAC/B,IAAI;QACHwD,KAAK,EAAE6B,IAAI,CAACpB,KAAM;QAClBqC,IAAI,EAAC,OAAO;QACZ3D,KAAK,EAAE0C,IAAI,CAAC1C,KAAK,IAAW,SAAU;QACtC+C,EAAE,EAAE;UACFa,MAAM,EAAE,EAAE;UACVJ,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd;MAAE;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EACAwB,IAAI,CAAC1C,KAAK,IAAI0C,IAAI,CAACpB,KAAK,KAAKoC,SAAS,iBACrCtE,OAAA,CAAChC,GAAG;QACF2F,EAAE,EAAE;UACFc,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE,CAAC;UACTZ,YAAY,EAAE,KAAK;UACnBG,eAAe,EAAEpD,mBAAmB,CAAC2C,IAAI,CAAC1C,KAAK;QACjD;MAAE;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC,GA/DJwB,IAAI,CAAC9B,EAAE;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAgEZ,CACX;EAED,MAAM4C,aAAa,gBACjB1E,OAAA,CAAChC,GAAG;IAAC2F,EAAE,EAAE;MAAEa,MAAM,EAAE,MAAM;MAAEG,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAApB,QAAA,gBAEpExD,OAAA,CAAChC,GAAG;MACF2F,EAAE,EAAE;QACFkB,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,aAAaxE,KAAK,CAACO,OAAO,CAACkE,OAAO,EAAE;QAClDC,UAAU,EAAE,2BAA2B1E,KAAK,CAACO,OAAO,CAACM,OAAO,CAACJ,IAAI,QAAQT,KAAK,CAACO,OAAO,CAACM,OAAO,CAAC8D,IAAI,QAAQ;QAC3GrE,KAAK,EAAE;MACT,CAAE;MAAA4C,QAAA,gBAEFxD,OAAA,CAACjC,UAAU;QAACmH,OAAO,EAAC,IAAI;QAACb,UAAU,EAAE,GAAI;QAAAb,QAAA,EAAC;MAE1C;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9B,OAAA,CAACjC,UAAU;QAACmH,OAAO,EAAC,OAAO;QAACvB,EAAE,EAAE;UAAEwB,OAAO,EAAE;QAAI,CAAE;QAAA3B,QAAA,EAAC;MAElD;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN9B,OAAA,CAAChC,GAAG;MAAC2F,EAAE,EAAE;QAAEkB,CAAC,EAAE,CAAC;QAAEC,YAAY,EAAE,aAAaxE,KAAK,CAACO,OAAO,CAACkE,OAAO;MAAG,CAAE;MAAAvB,QAAA,eACpExD,OAAA,CAAChC,GAAG;QAAC2F,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACzDxD,OAAA,CAAChC,GAAG;UACF2F,EAAE,EAAE;YACFc,KAAK,EAAE,CAAC;YACRD,MAAM,EAAE,CAAC;YACTZ,YAAY,EAAE,KAAK;YACnBG,eAAe,EAAEtD,KAAK,CAAC6E,WAAW,GAC9BhF,KAAK,CAACO,OAAO,CAACC,OAAO,CAACC,IAAI,GAC1BT,KAAK,CAACO,OAAO,CAACG,KAAK,CAACD;UAC1B;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF9B,OAAA,CAACjC,UAAU;UAACmH,OAAO,EAAC,SAAS;UAACtE,KAAK,EAAC,gBAAgB;UAAA4C,QAAA,EACjD/C,KAAK,CAAC6E,WAAW,GAAG,qBAAqB,GAAG;QAAc;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA,CAAChC,GAAG;MAAC2F,EAAE,EAAE;QAAE4B,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,EAC3CnC,YAAY,CAACqE,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC/B5F,OAAA,CAAChC,GAAG;QAAAwF,QAAA,gBACFxD,OAAA,CAACjC,UAAU;UACTmH,OAAO,EAAC,UAAU;UAClBvB,EAAE,EAAE;YACFkC,EAAE,EAAE,CAAC;YACLJ,EAAE,EAAE,CAAC;YACLd,OAAO,EAAE,OAAO;YAChB/D,KAAK,EAAEN,KAAK,CAACO,OAAO,CAACqD,IAAI,CAAC9C,SAAS;YACnCgD,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,GAAG;YACfyB,aAAa,EAAE;UACjB,CAAE;UAAAtC,QAAA,EAEDmC,OAAO,CAACrE;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACb9B,OAAA,CAACvC,IAAI;UAACsI,KAAK;UAACpC,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,EACvBmC,OAAO,CAACpE,KAAK,CAACmE,GAAG,CAACrC,cAAc;QAAC;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACN8D,KAAK,GAAGvE,YAAY,CAACe,MAAM,GAAG,CAAC,iBAC9BpC,OAAA,CAAClC,OAAO;UAAC6F,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEmC,EAAE,EAAE;UAAE;QAAE;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjC;MAAA,GApBO6D,OAAO,CAACrE,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBlB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9B,OAAA,CAAChC,GAAG;MACF2F,EAAE,EAAE;QACFkB,CAAC,EAAE,CAAC;QACJoB,SAAS,EAAE,aAAa3F,KAAK,CAACO,OAAO,CAACkE,OAAO,EAAE;QAC/ChB,eAAe,EAAE5F,KAAK,CAACmC,KAAK,CAACO,OAAO,CAACmE,UAAU,CAACkB,KAAK,EAAE,GAAG;MAC5D,CAAE;MAAA1C,QAAA,gBAEFxD,OAAA,CAACjC,UAAU;QAACmH,OAAO,EAAC,SAAS;QAACtE,KAAK,EAAC,gBAAgB;QAAC+D,OAAO,EAAC,OAAO;QAAAnB,QAAA,EAAC;MAErE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9B,OAAA,CAACjC,UAAU;QAACmH,OAAO,EAAC,SAAS;QAACtE,KAAK,EAAC,gBAAgB;QAAC+D,OAAO,EAAC,OAAO;QAAAnB,QAAA,GAAC,gBACrD,EAAC,IAAI2C,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAAA;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE9B,OAAA,CAAAE,SAAA;IAAAsD,QAAA,gBAEExD,OAAA,CAACxC,MAAM;MACL0H,OAAO,EAAC,YAAY;MACpBmB,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE7F,KAAK,CAAC8F,WAAY;MACxB5C,EAAE,EAAE;QACFgB,OAAO,EAAE;UAAE6B,EAAE,EAAE,MAAM;UAAEvD,EAAE,EAAE;QAAQ,CAAC;QACpC,oBAAoB,EAAE;UACpBwB,KAAK,EAAEtE,YAAY;UACnBsG,SAAS,EAAE,YAAY;UACvBC,WAAW,EAAE,aAAapG,KAAK,CAACO,OAAO,CAACkE,OAAO,EAAE;UACjDhB,eAAe,EAAEzD,KAAK,CAACO,OAAO,CAACmE,UAAU,CAACkB;QAC5C;MACF,CAAE;MAAA1C,QAAA,EAEDkB;IAAa;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGT9B,OAAA,CAACxC,MAAM;MACL0H,OAAO,EAAC,WAAW;MACnBmB,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE7F,KAAK,CAAC8F,WAAY;MACxBI,OAAO,EAAEA,CAAA,KAAMjG,cAAc,CAAC,KAAK,CAAE;MACrCkG,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;MACFlD,EAAE,EAAE;QACFgB,OAAO,EAAE;UAAE6B,EAAE,EAAE,OAAO;UAAEvD,EAAE,EAAE;QAAO,CAAC;QACpC,oBAAoB,EAAE;UACpBwB,KAAK,EAAEtE,YAAY;UACnBsG,SAAS,EAAE;QACb;MACF,CAAE;MAAAjD,QAAA,EAEDkB;IAAa;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAACzB,EAAA,CA5UID,OAAiB;EAAA,QACPlC,QAAQ,EACL0B,WAAW,EACXC,WAAW,EACMC,aAAa;AAAA;AAAAgH,EAAA,GAJ3C1G,OAAiB;AA8UvB,eAAeA,OAAO;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}