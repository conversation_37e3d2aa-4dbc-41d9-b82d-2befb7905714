from fastapi import APIRouter, HTTPException, BackgroundTasks
from .service import FlightSearch
from app.microservices.flight_service.schemas import FlightSearchRequest, SearchListRequest

# Create an instance of APIRouter
router = APIRouter()
flight_search = FlightSearch()

@router.post("/search")
async def search(request: FlightSearchRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to search for flights based on request data - matches YAML specification exactly.
    :param request: The search criteria for flights.
    :param background_tasks: Background tasks for any additional processing.
    :return: Search results for flights.
    """
    request_data = request.model_dump()
    result = flight_search.search(request_data)
    return result

@router.post("/search_list")
async def get_search(request: SearchListRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to retrieve a list of flight searches based on request data - matches YAML specification exactly.
    :param request: The criteria for retrieving search lists.
    :param background_tasks: Background tasks for any additional processing.
    :return: A list of search results.
    """
    request_data = request.model_dump()
    result = flight_search.search_list(request_data)
    return result


