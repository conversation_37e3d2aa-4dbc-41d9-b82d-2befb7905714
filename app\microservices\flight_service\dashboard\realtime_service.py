"""
Real-time dashboard service with WebSocket support for live monitoring.
Provides real-time metrics streaming, alert notifications, and interactive dashboards.
"""

import asyncio
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Set, Optional
from dataclasses import dataclass, asdict
import uuid

from fastapi import WebSocket, WebSocketDisconnect
from app.microservices.flight_service.analytics.performance_service import performance_analytics
from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.cache_warming.service import cache_warming_service
from app.microservices.flight_service.utils.async_provider_utils import async_provider_client
from app.microservices.flight_service.database.connection_pool import db_connection_pool


@dataclass
class DashboardClient:
    """WebSocket client connection information."""
    client_id: str
    websocket: WebSocket
    connected_at: float
    subscriptions: Set[str]
    last_ping: float


@dataclass
class RealTimeMetric:
    """Real-time metric data structure."""
    metric_id: str
    timestamp: float
    service: str
    metric_type: str
    value: float
    metadata: Dict[str, Any]
    alert_level: Optional[str] = None


class RealTimeDashboardService:
    """
    Real-time dashboard service with WebSocket streaming and live monitoring.
    """
    
    def __init__(self):
        self.connected_clients: Dict[str, DashboardClient] = {}
        self.metric_buffer: List[RealTimeMetric] = []
        self.alert_history: List[Dict[str, Any]] = []
        
        # Dashboard configuration
        self.config = {
            "metric_buffer_size": 1000,
            "alert_history_size": 100,
            "ping_interval": 30,  # seconds
            "metric_broadcast_interval": 5,  # seconds
            "client_timeout": 300  # seconds
        }
        
        # Service statistics
        self.service_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "metrics_broadcasted": 0,
            "alerts_sent": 0,
            "uptime_start": time.time()
        }
        
        # Start background tasks
        self._background_tasks = []
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """Start background tasks for dashboard service."""
        self._background_tasks = [
            asyncio.create_task(self._metrics_collector_loop()),
            asyncio.create_task(self._client_ping_loop()),
            asyncio.create_task(self._metric_broadcaster_loop())
        ]
    
    async def connect_client(self, websocket: WebSocket) -> str:
        """
        Connect a new WebSocket client to the dashboard.
        
        Args:
            websocket: WebSocket connection
            
        Returns:
            Client ID
        """
        await websocket.accept()
        
        client_id = str(uuid.uuid4())
        client = DashboardClient(
            client_id=client_id,
            websocket=websocket,
            connected_at=time.time(),
            subscriptions=set(),
            last_ping=time.time()
        )
        
        self.connected_clients[client_id] = client
        self.service_stats["total_connections"] += 1
        self.service_stats["active_connections"] += 1
        
        # Send welcome message
        await self._send_to_client(client_id, {
            "type": "connection_established",
            "client_id": client_id,
            "timestamp": time.time(),
            "available_subscriptions": [
                "system_metrics",
                "performance_alerts",
                "cache_statistics",
                "database_metrics",
                "booking_analytics"
            ]
        })
        
        print(f"Dashboard client connected: {client_id}")
        return client_id
    
    async def disconnect_client(self, client_id: str):
        """
        Disconnect a WebSocket client from the dashboard.
        
        Args:
            client_id: Client identifier
        """
        if client_id in self.connected_clients:
            del self.connected_clients[client_id]
            self.service_stats["active_connections"] -= 1
            print(f"Dashboard client disconnected: {client_id}")
    
    async def handle_client_message(self, client_id: str, message: Dict[str, Any]):
        """
        Handle incoming message from WebSocket client.
        
        Args:
            client_id: Client identifier
            message: Message from client
        """
        try:
            message_type = message.get("type")
            
            if message_type == "subscribe":
                await self._handle_subscription(client_id, message)
            elif message_type == "unsubscribe":
                await self._handle_unsubscription(client_id, message)
            elif message_type == "ping":
                await self._handle_ping(client_id)
            elif message_type == "get_historical_data":
                await self._handle_historical_data_request(client_id, message)
            else:
                await self._send_error(client_id, f"Unknown message type: {message_type}")
                
        except Exception as e:
            await self._send_error(client_id, f"Error handling message: {str(e)}")
    
    async def _handle_subscription(self, client_id: str, message: Dict[str, Any]):
        """Handle subscription request."""
        if client_id not in self.connected_clients:
            return
        
        subscription = message.get("subscription")
        if subscription:
            self.connected_clients[client_id].subscriptions.add(subscription)
            
            await self._send_to_client(client_id, {
                "type": "subscription_confirmed",
                "subscription": subscription,
                "timestamp": time.time()
            })
            
            # Send initial data for the subscription
            await self._send_initial_subscription_data(client_id, subscription)
    
    async def _handle_unsubscription(self, client_id: str, message: Dict[str, Any]):
        """Handle unsubscription request."""
        if client_id not in self.connected_clients:
            return
        
        subscription = message.get("subscription")
        if subscription:
            self.connected_clients[client_id].subscriptions.discard(subscription)
            
            await self._send_to_client(client_id, {
                "type": "unsubscription_confirmed",
                "subscription": subscription,
                "timestamp": time.time()
            })
    
    async def _handle_ping(self, client_id: str):
        """Handle ping from client."""
        if client_id in self.connected_clients:
            self.connected_clients[client_id].last_ping = time.time()
            
            await self._send_to_client(client_id, {
                "type": "pong",
                "timestamp": time.time()
            })
    
    async def _handle_historical_data_request(self, client_id: str, message: Dict[str, Any]):
        """Handle request for historical data."""
        try:
            data_type = message.get("data_type")
            hours = message.get("hours", 1)
            
            if data_type == "performance_report":
                report = await performance_analytics.generate_performance_report(hours)
                await self._send_to_client(client_id, {
                    "type": "historical_data",
                    "data_type": data_type,
                    "data": report,
                    "timestamp": time.time()
                })
            elif data_type == "alert_history":
                await self._send_to_client(client_id, {
                    "type": "historical_data",
                    "data_type": data_type,
                    "data": self.alert_history[-50:],  # Last 50 alerts
                    "timestamp": time.time()
                })
                
        except Exception as e:
            await self._send_error(client_id, f"Error retrieving historical data: {str(e)}")
    
    async def _send_initial_subscription_data(self, client_id: str, subscription: str):
        """Send initial data for a new subscription."""
        try:
            if subscription == "system_metrics":
                dashboard_data = await performance_analytics.get_performance_dashboard()
                await self._send_to_client(client_id, {
                    "type": "initial_data",
                    "subscription": subscription,
                    "data": dashboard_data,
                    "timestamp": time.time()
                })
            
            elif subscription == "cache_statistics":
                cache_stats = await flight_cache_service.get_cache_statistics()
                await self._send_to_client(client_id, {
                    "type": "initial_data",
                    "subscription": subscription,
                    "data": cache_stats,
                    "timestamp": time.time()
                })
            
            elif subscription == "database_metrics":
                db_stats = db_connection_pool.get_performance_stats()
                await self._send_to_client(client_id, {
                    "type": "initial_data",
                    "subscription": subscription,
                    "data": db_stats,
                    "timestamp": time.time()
                })
                
        except Exception as e:
            await self._send_error(client_id, f"Error sending initial data: {str(e)}")
    
    async def _send_to_client(self, client_id: str, data: Dict[str, Any]):
        """Send data to a specific client."""
        if client_id in self.connected_clients:
            try:
                await self.connected_clients[client_id].websocket.send_text(json.dumps(data))
            except Exception as e:
                print(f"Error sending to client {client_id}: {str(e)}")
                await self.disconnect_client(client_id)
    
    async def _send_error(self, client_id: str, error_message: str):
        """Send error message to client."""
        await self._send_to_client(client_id, {
            "type": "error",
            "message": error_message,
            "timestamp": time.time()
        })
    
    async def broadcast_metric(self, metric: RealTimeMetric):
        """
        Broadcast a real-time metric to subscribed clients.
        
        Args:
            metric: Real-time metric to broadcast
        """
        # Add to metric buffer
        self.metric_buffer.append(metric)
        if len(self.metric_buffer) > self.config["metric_buffer_size"]:
            self.metric_buffer.pop(0)
        
        # Determine subscription type
        subscription_map = {
            "cache_service": "cache_statistics",
            "database": "database_metrics",
            "booking_service": "booking_analytics",
            "provider_client": "system_metrics"
        }
        
        subscription = subscription_map.get(metric.service, "system_metrics")
        
        # Broadcast to subscribed clients
        message = {
            "type": "real_time_metric",
            "subscription": subscription,
            "metric": asdict(metric),
            "timestamp": time.time()
        }
        
        for client_id, client in self.connected_clients.items():
            if subscription in client.subscriptions:
                await self._send_to_client(client_id, message)
        
        self.service_stats["metrics_broadcasted"] += 1
    
    async def broadcast_alert(self, alert: Dict[str, Any]):
        """
        Broadcast an alert to all subscribed clients.
        
        Args:
            alert: Alert information
        """
        # Add to alert history
        self.alert_history.append({
            **alert,
            "timestamp": time.time()
        })
        
        if len(self.alert_history) > self.config["alert_history_size"]:
            self.alert_history.pop(0)
        
        # Broadcast to all clients subscribed to alerts
        message = {
            "type": "performance_alert",
            "alert": alert,
            "timestamp": time.time()
        }
        
        for client_id, client in self.connected_clients.items():
            if "performance_alerts" in client.subscriptions:
                await self._send_to_client(client_id, message)
        
        self.service_stats["alerts_sent"] += 1
    
    async def _metrics_collector_loop(self):
        """Background loop to collect and broadcast metrics."""
        while True:
            try:
                await asyncio.sleep(self.config["metric_broadcast_interval"])
                
                # Collect current metrics
                current_time = time.time()
                
                # Cache metrics
                try:
                    cache_stats = await flight_cache_service.get_cache_statistics()
                    cache_metric = RealTimeMetric(
                        metric_id=f"cache_hit_rate_{int(current_time)}",
                        timestamp=current_time,
                        service="cache_service",
                        metric_type="cache_hit_rate",
                        value=cache_stats.get("hit_rate_percentage", 0) / 100,
                        metadata=cache_stats
                    )
                    await self.broadcast_metric(cache_metric)
                except Exception as e:
                    print(f"Error collecting cache metrics: {str(e)}")
                
                # Database metrics
                try:
                    db_stats = db_connection_pool.get_performance_stats()
                    query_stats = db_stats.get("query_stats", {})
                    
                    db_metric = RealTimeMetric(
                        metric_id=f"db_cache_hit_rate_{int(current_time)}",
                        timestamp=current_time,
                        service="database",
                        metric_type="database_cache_hit_rate",
                        value=query_stats.get("cache_hit_rate", 0),
                        metadata=db_stats
                    )
                    await self.broadcast_metric(db_metric)
                except Exception as e:
                    print(f"Error collecting database metrics: {str(e)}")
                
            except Exception as e:
                print(f"Error in metrics collector loop: {str(e)}")
    
    async def _client_ping_loop(self):
        """Background loop to ping clients and remove inactive ones."""
        while True:
            try:
                await asyncio.sleep(self.config["ping_interval"])
                
                current_time = time.time()
                inactive_clients = []
                
                for client_id, client in self.connected_clients.items():
                    # Check if client is inactive
                    if current_time - client.last_ping > self.config["client_timeout"]:
                        inactive_clients.append(client_id)
                    else:
                        # Send ping
                        await self._send_to_client(client_id, {
                            "type": "ping",
                            "timestamp": current_time
                        })
                
                # Remove inactive clients
                for client_id in inactive_clients:
                    await self.disconnect_client(client_id)
                    
            except Exception as e:
                print(f"Error in client ping loop: {str(e)}")
    
    async def _metric_broadcaster_loop(self):
        """Background loop for additional metric broadcasting."""
        while True:
            try:
                await asyncio.sleep(10)  # Every 10 seconds
                
                # Broadcast system status
                system_status = {
                    "active_connections": self.service_stats["active_connections"],
                    "uptime_seconds": time.time() - self.service_stats["uptime_start"],
                    "metrics_broadcasted": self.service_stats["metrics_broadcasted"],
                    "alerts_sent": self.service_stats["alerts_sent"]
                }
                
                for client_id, client in self.connected_clients.items():
                    if "system_metrics" in client.subscriptions:
                        await self._send_to_client(client_id, {
                            "type": "system_status",
                            "status": system_status,
                            "timestamp": time.time()
                        })
                        
            except Exception as e:
                print(f"Error in metric broadcaster loop: {str(e)}")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get dashboard service statistics."""
        return {
            **self.service_stats,
            "metric_buffer_size": len(self.metric_buffer),
            "alert_history_size": len(self.alert_history),
            "background_tasks_running": len([t for t in self._background_tasks if not t.done()]),
            "config": self.config
        }
    
    async def shutdown(self):
        """Shutdown the dashboard service."""
        # Cancel background tasks
        for task in self._background_tasks:
            task.cancel()
        
        # Disconnect all clients
        for client_id in list(self.connected_clients.keys()):
            await self.disconnect_client(client_id)
        
        print("Real-time dashboard service shutdown complete")


# Global real-time dashboard service instance
realtime_dashboard = RealTimeDashboardService()
