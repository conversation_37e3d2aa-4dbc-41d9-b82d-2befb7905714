"""
Comprehensive performance analytics service for monitoring and optimization.
Provides real-time metrics, trend analysis, and performance insights.
"""

import asyncio
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.cache_warming.service import cache_warming_service
from app.microservices.flight_service.utils.async_provider_utils import async_provider_client
from app.microservices.flight_service.utils.request_deduplication import request_deduplicator
from app.microservices.flight_service.database.connection_pool import db_connection_pool


@dataclass
class PerformanceMetric:
    """Individual performance metric data point."""
    timestamp: float
    value: float
    metric_type: str
    service: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrendAnalysis:
    """Trend analysis results."""
    direction: str  # 'improving', 'declining', 'stable'
    change_percentage: float
    confidence: float
    data_points: int
    time_period: str


class PerformanceAnalyticsService:
    """
    Comprehensive performance analytics service with real-time monitoring and trend analysis.
    """
    
    def __init__(self):
        self.redis_client = RedisCache.connection()
        
        # Metrics storage (in-memory for real-time analysis)
        self.metrics_buffer: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.aggregated_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Performance thresholds
        self.thresholds = {
            "response_time_warning": 1000,  # ms
            "response_time_critical": 3000,  # ms
            "cache_hit_rate_warning": 0.7,  # 70%
            "cache_hit_rate_critical": 0.5,  # 50%
            "error_rate_warning": 0.05,  # 5%
            "error_rate_critical": 0.1,  # 10%
            "database_query_time_warning": 500,  # ms
            "database_query_time_critical": 1000  # ms
        }
        
        # Analytics state
        self.analytics_stats = {
            "total_metrics_collected": 0,
            "alerts_generated": 0,
            "trend_analyses_performed": 0,
            "performance_reports_generated": 0
        }
        
        # Start background analytics task
        self._analytics_task = None
        self._start_analytics_task()
    
    def _start_analytics_task(self):
        """Start background analytics processing task."""
        if self._analytics_task is None or self._analytics_task.done():
            self._analytics_task = asyncio.create_task(self._analytics_loop())
    
    async def _analytics_loop(self):
        """Background analytics processing loop."""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self._collect_system_metrics()
                await self._perform_trend_analysis()
                await self._cleanup_old_metrics()
                
            except Exception as e:
                print(f"Analytics loop error: {str(e)}")
    
    async def record_metric(
        self, 
        metric_type: str, 
        value: float, 
        service: str, 
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Record a performance metric.
        
        Args:
            metric_type: Type of metric (response_time, cache_hit_rate, etc.)
            value: Metric value
            service: Service name
            metadata: Additional metadata
        """
        metric = PerformanceMetric(
            timestamp=time.time(),
            value=value,
            metric_type=metric_type,
            service=service,
            metadata=metadata or {}
        )
        
        metric_key = f"{service}:{metric_type}"
        self.metrics_buffer[metric_key].append(metric)
        self.analytics_stats["total_metrics_collected"] += 1
        
        # Check for immediate alerts
        await self._check_metric_thresholds(metric)
    
    async def _collect_system_metrics(self):
        """Collect metrics from all system components."""
        try:
            # Cache service metrics
            cache_stats = await flight_cache_service.get_cache_statistics()
            await self.record_metric(
                "cache_hit_rate", 
                cache_stats.get("hit_rate_percentage", 0) / 100,
                "cache_service",
                {"total_requests": cache_stats.get("total_requests", 0)}
            )
            
            # Cache warming metrics
            warming_stats = cache_warming_service.get_warming_stats()
            await self.record_metric(
                "warming_tasks_success_rate",
                warming_stats.get("successful_warmings", 0) / max(warming_stats.get("total_warming_requests", 1), 1),
                "cache_warming",
                warming_stats
            )
            
            # Provider client metrics
            provider_stats = async_provider_client.get_stats()
            request_stats = provider_stats.get("request_stats", {})
            total_requests = request_stats.get("total_requests", 0)
            
            if total_requests > 0:
                await self.record_metric(
                    "provider_success_rate",
                    request_stats.get("successful_requests", 0) / total_requests,
                    "provider_client",
                    request_stats
                )
            
            # Deduplication metrics
            dedup_stats = request_deduplicator.get_stats()
            total_dedup_requests = dedup_stats.get("total_requests", 0)
            
            if total_dedup_requests > 0:
                await self.record_metric(
                    "deduplication_rate",
                    dedup_stats.get("deduplicated_requests", 0) / total_dedup_requests,
                    "deduplication",
                    dedup_stats
                )
            
            # Database metrics
            db_stats = db_connection_pool.get_performance_stats()
            query_stats = db_stats.get("query_stats", {})
            
            await self.record_metric(
                "database_cache_hit_rate",
                query_stats.get("cache_hit_rate", 0),
                "database",
                query_stats
            )
            
        except Exception as e:
            print(f"Error collecting system metrics: {str(e)}")
    
    async def _check_metric_thresholds(self, metric: PerformanceMetric):
        """Check if metric exceeds thresholds and generate alerts."""
        metric_key = f"{metric.service}:{metric.metric_type}"
        
        # Define threshold checks
        threshold_checks = {
            "response_time": {
                "warning": self.thresholds["response_time_warning"],
                "critical": self.thresholds["response_time_critical"],
                "comparison": "greater"
            },
            "cache_hit_rate": {
                "warning": self.thresholds["cache_hit_rate_warning"],
                "critical": self.thresholds["cache_hit_rate_critical"],
                "comparison": "less"
            },
            "error_rate": {
                "warning": self.thresholds["error_rate_warning"],
                "critical": self.thresholds["error_rate_critical"],
                "comparison": "greater"
            }
        }
        
        for check_type, thresholds in threshold_checks.items():
            if check_type in metric.metric_type:
                warning_threshold = thresholds["warning"]
                critical_threshold = thresholds["critical"]
                comparison = thresholds["comparison"]
                
                if comparison == "greater":
                    if metric.value > critical_threshold:
                        await self._generate_alert("critical", metric, critical_threshold)
                    elif metric.value > warning_threshold:
                        await self._generate_alert("warning", metric, warning_threshold)
                else:  # less
                    if metric.value < critical_threshold:
                        await self._generate_alert("critical", metric, critical_threshold)
                    elif metric.value < warning_threshold:
                        await self._generate_alert("warning", metric, warning_threshold)
    
    async def _generate_alert(self, severity: str, metric: PerformanceMetric, threshold: float):
        """Generate performance alert."""
        alert = {
            "severity": severity,
            "metric_type": metric.metric_type,
            "service": metric.service,
            "current_value": metric.value,
            "threshold": threshold,
            "timestamp": metric.timestamp,
            "metadata": metric.metadata
        }
        
        print(f"PERFORMANCE ALERT [{severity.upper()}]: {metric.service}.{metric.metric_type} = {metric.value} (threshold: {threshold})")
        
        # Store alert in Redis for dashboard
        alert_key = f"performance_alert:{int(metric.timestamp)}"
        try:
            self.redis_client.set_cache(alert_key, alert, 86400)  # 24 hours
        except Exception as e:
            print(f"Failed to store alert: {str(e)}")
        
        self.analytics_stats["alerts_generated"] += 1
    
    async def _perform_trend_analysis(self):
        """Perform trend analysis on collected metrics."""
        try:
            for metric_key, metrics_deque in self.metrics_buffer.items():
                if len(metrics_deque) >= 10:  # Need at least 10 data points
                    trend = self._analyze_trend(list(metrics_deque))
                    
                    # Store trend analysis
                    self.aggregated_metrics[metric_key] = {
                        "trend": trend,
                        "last_updated": time.time(),
                        "data_points": len(metrics_deque)
                    }
                    
                    self.analytics_stats["trend_analyses_performed"] += 1
                    
        except Exception as e:
            print(f"Error performing trend analysis: {str(e)}")
    
    def _analyze_trend(self, metrics: List[PerformanceMetric]) -> TrendAnalysis:
        """
        Analyze trend in metric values.
        
        Args:
            metrics: List of metric data points
            
        Returns:
            Trend analysis results
        """
        if len(metrics) < 2:
            return TrendAnalysis("stable", 0.0, 0.0, len(metrics), "insufficient_data")
        
        # Extract values and timestamps
        values = [m.value for m in metrics]
        timestamps = [m.timestamp for m in metrics]
        
        # Calculate trend using linear regression slope
        n = len(values)
        sum_x = sum(range(n))
        sum_y = sum(values)
        sum_xy = sum(i * values[i] for i in range(n))
        sum_x2 = sum(i * i for i in range(n))
        
        # Linear regression slope
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        # Calculate percentage change
        first_value = values[0]
        last_value = values[-1]
        change_percentage = ((last_value - first_value) / first_value * 100) if first_value != 0 else 0
        
        # Determine trend direction
        if abs(change_percentage) < 5:  # Less than 5% change
            direction = "stable"
        elif slope > 0:
            direction = "improving" if metrics[0].metric_type in ["cache_hit_rate", "success_rate"] else "declining"
        else:
            direction = "declining" if metrics[0].metric_type in ["cache_hit_rate", "success_rate"] else "improving"
        
        # Calculate confidence based on data consistency
        variance = statistics.variance(values) if len(values) > 1 else 0
        mean_value = statistics.mean(values)
        coefficient_of_variation = (variance ** 0.5) / mean_value if mean_value != 0 else 0
        confidence = max(0, 1 - coefficient_of_variation)
        
        # Time period
        time_span = timestamps[-1] - timestamps[0]
        if time_span < 3600:
            time_period = f"{int(time_span / 60)} minutes"
        elif time_span < 86400:
            time_period = f"{int(time_span / 3600)} hours"
        else:
            time_period = f"{int(time_span / 86400)} days"
        
        return TrendAnalysis(
            direction=direction,
            change_percentage=change_percentage,
            confidence=confidence,
            data_points=len(metrics),
            time_period=time_period
        )
    
    async def _cleanup_old_metrics(self):
        """Clean up old metrics to prevent memory bloat."""
        cutoff_time = time.time() - 86400  # 24 hours
        
        for metric_key, metrics_deque in self.metrics_buffer.items():
            # Remove old metrics
            while metrics_deque and metrics_deque[0].timestamp < cutoff_time:
                metrics_deque.popleft()
    
    async def get_performance_dashboard(self) -> Dict[str, Any]:
        """
        Get comprehensive performance dashboard data.
        
        Returns:
            Dashboard data with metrics, trends, and alerts
        """
        try:
            # Current metrics summary
            current_metrics = {}
            for metric_key, metrics_deque in self.metrics_buffer.items():
                if metrics_deque:
                    latest_metric = metrics_deque[-1]
                    current_metrics[metric_key] = {
                        "current_value": latest_metric.value,
                        "timestamp": latest_metric.timestamp,
                        "service": latest_metric.service,
                        "metric_type": latest_metric.metric_type
                    }
            
            # Trend analysis
            trends = {}
            for metric_key, aggregated_data in self.aggregated_metrics.items():
                trend = aggregated_data.get("trend")
                if trend:
                    trends[metric_key] = {
                        "direction": trend.direction,
                        "change_percentage": trend.change_percentage,
                        "confidence": trend.confidence,
                        "time_period": trend.time_period
                    }
            
            # Recent alerts (last 24 hours)
            recent_alerts = []
            try:
                # This would need to be implemented to fetch from Redis
                # For now, return empty list
                pass
            except Exception:
                pass
            
            # System health summary
            health_summary = await self._calculate_system_health()
            
            return {
                "dashboard_generated_at": datetime.now(timezone.utc).isoformat(),
                "system_health": health_summary,
                "current_metrics": current_metrics,
                "trends": trends,
                "recent_alerts": recent_alerts,
                "analytics_stats": self.analytics_stats,
                "thresholds": self.thresholds
            }
            
        except Exception as e:
            print(f"Error generating performance dashboard: {str(e)}")
            return {"error": str(e)}
    
    async def _calculate_system_health(self) -> Dict[str, Any]:
        """Calculate overall system health score."""
        try:
            health_scores = []
            health_details = {}
            
            # Cache service health
            cache_metrics = [m for m in self.metrics_buffer.get("cache_service:cache_hit_rate", []) if m.timestamp > time.time() - 3600]
            if cache_metrics:
                avg_hit_rate = statistics.mean([m.value for m in cache_metrics])
                cache_health = min(1.0, avg_hit_rate / 0.8)  # 80% is considered healthy
                health_scores.append(cache_health)
                health_details["cache_service"] = {
                    "score": cache_health,
                    "avg_hit_rate": avg_hit_rate,
                    "status": "healthy" if cache_health > 0.8 else "warning" if cache_health > 0.6 else "critical"
                }
            
            # Provider client health
            provider_metrics = [m for m in self.metrics_buffer.get("provider_client:provider_success_rate", []) if m.timestamp > time.time() - 3600]
            if provider_metrics:
                avg_success_rate = statistics.mean([m.value for m in provider_metrics])
                provider_health = avg_success_rate
                health_scores.append(provider_health)
                health_details["provider_client"] = {
                    "score": provider_health,
                    "avg_success_rate": avg_success_rate,
                    "status": "healthy" if provider_health > 0.9 else "warning" if provider_health > 0.8 else "critical"
                }
            
            # Database health
            db_metrics = [m for m in self.metrics_buffer.get("database:database_cache_hit_rate", []) if m.timestamp > time.time() - 3600]
            if db_metrics:
                avg_db_hit_rate = statistics.mean([m.value for m in db_metrics])
                db_health = min(1.0, avg_db_hit_rate / 0.7)  # 70% is considered healthy for DB cache
                health_scores.append(db_health)
                health_details["database"] = {
                    "score": db_health,
                    "avg_cache_hit_rate": avg_db_hit_rate,
                    "status": "healthy" if db_health > 0.8 else "warning" if db_health > 0.6 else "critical"
                }
            
            # Overall health score
            overall_health = statistics.mean(health_scores) if health_scores else 0.5
            
            if overall_health > 0.8:
                overall_status = "healthy"
            elif overall_health > 0.6:
                overall_status = "warning"
            else:
                overall_status = "critical"
            
            return {
                "overall_score": overall_health,
                "overall_status": overall_status,
                "component_health": health_details,
                "last_calculated": time.time()
            }
            
        except Exception as e:
            print(f"Error calculating system health: {str(e)}")
            return {
                "overall_score": 0.0,
                "overall_status": "unknown",
                "error": str(e)
            }
    
    async def generate_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """
        Generate comprehensive performance report.
        
        Args:
            hours: Number of hours to include in report
            
        Returns:
            Performance report with detailed analysis
        """
        try:
            cutoff_time = time.time() - (hours * 3600)
            report_data = {
                "report_period": f"{hours} hours",
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "services": {}
            }
            
            # Analyze each service
            for metric_key, metrics_deque in self.metrics_buffer.items():
                service, metric_type = metric_key.split(":", 1)
                
                # Filter metrics for report period
                period_metrics = [m for m in metrics_deque if m.timestamp > cutoff_time]
                
                if period_metrics:
                    values = [m.value for m in period_metrics]
                    
                    if service not in report_data["services"]:
                        report_data["services"][service] = {}
                    
                    report_data["services"][service][metric_type] = {
                        "data_points": len(period_metrics),
                        "average": statistics.mean(values),
                        "median": statistics.median(values),
                        "min": min(values),
                        "max": max(values),
                        "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
                        "trend": self._analyze_trend(period_metrics).__dict__
                    }
            
            self.analytics_stats["performance_reports_generated"] += 1
            
            return report_data
            
        except Exception as e:
            print(f"Error generating performance report: {str(e)}")
            return {"error": str(e)}
    
    def get_analytics_stats(self) -> Dict[str, Any]:
        """Get analytics service statistics."""
        return {
            **self.analytics_stats,
            "metrics_buffer_size": sum(len(deque) for deque in self.metrics_buffer.values()),
            "unique_metric_types": len(self.metrics_buffer),
            "aggregated_metrics_count": len(self.aggregated_metrics),
            "analytics_task_running": self._analytics_task is not None and not self._analytics_task.done()
        }


# Global performance analytics service instance
performance_analytics = PerformanceAnalyticsService()
