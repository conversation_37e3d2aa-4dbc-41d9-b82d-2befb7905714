from fastapi import <PERSON>Router, HTTPException, BackgroundTasks
from app.microservices.flight_service.detail_service.service import FlightDetail
from app.microservices.flight_service.schemas import ServiceRequest

# Initialize the FastAPI router for special service requests
router = APIRouter()
flight_detail = FlightDetail()  # Create an instance of the FlightDetail service

@router.post("/service_req/")
async def special_service_request(request: ServiceRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to handle special service requests for flights - matches YAML specification exactly.

    Args:
        request (ServiceRequest): A request containing the parameters necessary to process
                                 the special service request. This may include service identifiers
                                 or related flight details.

        background_tasks (BackgroundTasks): Enables scheduling of background tasks that
                                             can be executed after the response is sent.

    Returns:
        dict: A dictionary containing the result of the special service request or
              an error message if the request fails.
    """
    try:
        # Call the detail method of the FlightDetail service to process the request
        request_data = request.model_dump()
        result = flight_detail.detail(request_data)
        return result  # Return the result of the special service request
    except HTTPException as e:
        # If an HTTP exception occurs, raise it to provide an appropriate error response
        raise e
    except Exception as e:
        # Handle any other exceptions that may arise
        raise HTTPException(status_code=500, detail=str(e))  # Return a 500 error with the exception message