#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Fast Travel Admin Dashboard - Setup Verification');
console.log('==================================================');

// Check if required files exist
const requiredFiles = [
  'package.json',
  'tsconfig.json',
  '.env',
  'src/App.tsx',
  'src/index.tsx',
  'src/types/index.ts',
  'src/services/api.ts',
  'src/services/websocket.ts',
  'src/hooks/useApi.ts',
  'src/hooks/useWebSocket.ts',
  'src/contexts/AppContext.tsx',
  'src/components/Layout/Layout.tsx',
  'src/components/Layout/Header.tsx',
  'src/components/Layout/Sidebar.tsx',
  'src/components/Dashboard/MetricsCard.tsx',
  'src/pages/Dashboard.tsx',
  'public/index.html',
  'public/manifest.json'
];

let allFilesExist = true;

console.log('\n📁 Checking required files...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

// Check package.json structure
console.log('\n📦 Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const requiredDeps = [
    'react',
    'react-dom',
    'react-scripts',
    'typescript',
    '@mui/material',
    '@mui/icons-material',
    '@emotion/react',
    '@emotion/styled',
    'axios',
    'react-router-dom',
    'socket.io-client',
    'recharts',
    'react-query',
    'react-hot-toast'
  ];
  
  const requiredDevDeps = [
    '@types/react',
    '@types/react-dom',
    '@types/node'
  ];
  
  console.log('Dependencies:');
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - MISSING`);
      allFilesExist = false;
    }
  });
  
  console.log('Dev Dependencies:');
  requiredDevDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - MISSING`);
      allFilesExist = false;
    }
  });
  
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  allFilesExist = false;
}

// Check environment file
console.log('\n🔧 Checking environment configuration...');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  const requiredEnvVars = [
    'REACT_APP_API_BASE_URL',
    'REACT_APP_WS_URL',
    'REACT_APP_ENVIRONMENT'
  ];
  
  requiredEnvVars.forEach(envVar => {
    if (envContent.includes(envVar)) {
      console.log(`✅ ${envVar}`);
    } else {
      console.log(`❌ ${envVar} - MISSING`);
    }
  });
} catch (error) {
  console.log('❌ Error reading .env file:', error.message);
}

// Check TypeScript configuration
console.log('\n⚙️ Checking TypeScript configuration...');
try {
  const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  
  if (tsConfig.compilerOptions) {
    console.log('✅ TypeScript compiler options configured');
    
    if (tsConfig.compilerOptions.jsx === 'react-jsx') {
      console.log('✅ JSX configuration correct');
    } else {
      console.log('❌ JSX configuration incorrect');
    }
    
    if (tsConfig.compilerOptions.baseUrl === 'src') {
      console.log('✅ Base URL configured');
    } else {
      console.log('❌ Base URL not configured');
    }
  } else {
    console.log('❌ TypeScript compiler options missing');
  }
} catch (error) {
  console.log('❌ Error reading tsconfig.json:', error.message);
}

// Check if node_modules exists
console.log('\n📚 Checking dependencies installation...');
if (fs.existsSync('node_modules')) {
  console.log('✅ node_modules directory exists');
  
  // Check for key packages
  const keyPackages = ['react', '@mui/material', 'typescript'];
  keyPackages.forEach(pkg => {
    if (fs.existsSync(path.join('node_modules', pkg))) {
      console.log(`✅ ${pkg} installed`);
    } else {
      console.log(`❌ ${pkg} not installed`);
    }
  });
} else {
  console.log('❌ node_modules directory missing - run "npm install"');
}

// Final summary
console.log('\n📋 Setup Summary');
console.log('================');

if (allFilesExist) {
  console.log('✅ All required files are present');
  console.log('✅ Package configuration looks good');
  
  if (fs.existsSync('node_modules')) {
    console.log('✅ Dependencies are installed');
    console.log('\n🚀 Ready to start! Run: npm start');
  } else {
    console.log('⚠️  Dependencies need to be installed');
    console.log('\n📦 Next step: npm install');
  }
} else {
  console.log('❌ Some files are missing or incorrectly configured');
  console.log('📖 Check the setup guide for missing components');
}

console.log('\n🔗 Useful commands:');
console.log('   npm install          - Install dependencies');
console.log('   npm start            - Start development server');
console.log('   npm run build        - Create production build');
console.log('   npm test             - Run tests');

console.log('\n📚 Documentation:');
console.log('   README.md            - Full documentation');
console.log('   TROUBLESHOOTING.md   - Error solutions');
console.log('   install-and-start.md - Quick start guide');
