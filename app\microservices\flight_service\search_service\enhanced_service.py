"""
Enhanced flight search service with improved caching, error handling, and performance optimization.
This service implements multi-layer caching, intelligent cache management, and robust fallback mechanisms.
"""
import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service, CacheStrategy
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import flight_search_translate_client_to_provider
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import SearchProviderResponseTranslator
from app.microservices.flight_service.search_service.tasks import fetch_flight_search_task
from app.microservices.flight_service.search_service.utils import generate_search_cache_key
from app.microservices.flight_service.utils.provider_utils import make_provider_api_call
from app.microservices.flight_service import config


class EnhancedFlightSearch:
    """
    Enhanced flight search service with improved caching and performance optimization.
    """
    
    def __init__(self):
        self.cache_service = flight_cache_service
        self.redis_client = RedisCache.connection()
        self.provider_timeout = 30  # seconds
        self.max_retries = 3
    
    async def search(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced flight search with multi-layer caching and intelligent fallback.
        
        Args:
            request_data (dict): The request data containing search parameters.
            
        Returns:
            dict: The response with flight search results.
        """
        start_time = time.time()
        
        try:
            # Step 1: Check multi-layer cache
            cached_results = await self.cache_service.get_flight_search_results(request_data)
            
            if cached_results:
                # Cache hit - trigger background refresh if data is getting stale
                await self._trigger_background_refresh_if_needed(request_data, cached_results)
                
                # Add performance metadata
                cached_results.update({
                    "sh_price": False,  # Indicates cached data
                    "cache_hit": True,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "cache"
                })
                
                return cached_results
            
            # Step 2: Cache miss - fetch from provider with fallback strategy
            return await self._fetch_with_fallback_strategy(request_data, start_time)
            
        except Exception as e:
            print(f"Search error: {str(e)}")
            return self._create_error_response(request_data, str(e))
    
    async def search_list(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced search list retrieval with better error handling.
        
        Args:
            request_data (dict): The request data containing the TUI.
            
        Returns:
            dict: The cached results or an error message if no results found.
        """
        cache_key = request_data.get('TUI')
        
        if not cache_key:
            return self._create_error_response(request_data, "Missing TUI parameter")
        
        try:
            # Attempt to retrieve results from cache
            results = self.redis_client.get_cache(cache_key)
            
            if not results:
                return {
                    "TUI": cache_key,
                    "Completed": False,
                    "CeilingInfo": None,
                    "CurrencyCode": "INR",
                    "Notices": ["Search results have expired. Please perform a new search."],
                    "Trips": None,
                    "Code": "404",
                    "Msg": ["Search results not found or expired"]
                }
            
            # Add metadata and return results
            results.update({
                "TUI": cache_key,
                "sh_price": True,
                "retrieved_at": datetime.utcnow().isoformat(),
                "data_source": "cache_retrieval"
            })
            
            return results
            
        except Exception as e:
            print(f"Search list error: {str(e)}")
            return self._create_error_response(request_data, str(e))
    
    async def _fetch_with_fallback_strategy(self, request_data: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """
        Fetch flight data with multiple fallback strategies.
        """
        # Strategy 1: Try direct provider call with timeout
        try:
            results = await self._fetch_from_provider_with_timeout(request_data)
            
            if results and results.get("Trips"):
                # Cache the successful results
                await self.cache_service.set_flight_search_results(
                    request_data, 
                    results, 
                    CacheStrategy.WRITE_THROUGH
                )
                
                results.update({
                    "sh_price": False,
                    "cache_hit": False,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "provider_direct"
                })
                
                return results
                
        except asyncio.TimeoutError:
            print("Provider timeout - falling back to background task")
        except Exception as e:
            print(f"Provider error: {str(e)} - falling back to background task")
        
        # Strategy 2: Fallback to background task with immediate response
        return await self._initiate_background_search(request_data, start_time)
    
    async def _fetch_from_provider_with_timeout(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Fetch data from provider with timeout protection.
        """
        provider_payload = flight_search_translate_client_to_provider(request_data)
        provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"
        
        # Use asyncio timeout for better control
        try:
            # This would need to be made async in the actual implementation
            provider_response = make_provider_api_call(provider_api_url, provider_payload)
            
            if provider_response:
                translator = SearchProviderResponseTranslator(provider_response, request_data)
                return translator.translate()
                
        except Exception as e:
            print(f"Provider call failed: {str(e)}")
            raise
        
        return None
    
    async def _initiate_background_search(self, request_data: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """
        Initiate background search and return immediate response.
        """
        cache_key = generate_search_cache_key(request_data)
        
        # Trigger background task
        fetch_flight_search_task.delay("BackgroundProvider", request_data, cache_key)
        
        # Return immediate response indicating search is in progress
        return {
            "TUI": cache_key,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": ["Search in progress. Please check back in a few moments."],
            "Trips": [],
            "Code": "202",  # Accepted - processing
            "Msg": ["Flight search initiated. Results will be available shortly."],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "background_task",
            "search_status": "processing"
        }
    
    async def _trigger_background_refresh_if_needed(self, request_data: Dict[str, Any], cached_results: Dict[str, Any]) -> None:
        """
        Trigger background refresh if cached data is getting stale.
        """
        try:
            cached_at_str = cached_results.get("cached_at")
            if cached_at_str:
                cached_at = datetime.fromisoformat(cached_at_str.replace('Z', '+00:00'))
                age_minutes = (datetime.utcnow() - cached_at).total_seconds() / 60
                
                # Refresh if data is older than 10 minutes
                if age_minutes > 10:
                    cache_key = generate_search_cache_key(request_data)
                    fetch_flight_search_task.delay("RefreshProvider", request_data, cache_key)
                    
        except Exception as e:
            print(f"Background refresh trigger error: {str(e)}")
    
    def _create_error_response(self, request_data: Dict[str, Any], error_message: str) -> Dict[str, Any]:
        """
        Create standardized error response.
        """
        return {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Internal server error occurred"],
            "sh_price": False,
            "cache_hit": False,
            "data_source": "error",
            "error_details": error_message
        }
    
    async def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get cache performance statistics.
        """
        return self.cache_service.get_cache_stats()
    
    async def invalidate_route_cache(self, from_code: str, to_code: str, date_str: str) -> Dict[str, Any]:
        """
        Invalidate cache for specific route.
        """
        invalidated_count = await self.cache_service.invalidate_route_cache(from_code, to_code, date_str)
        
        return {
            "status": "success",
            "invalidated_entries": invalidated_count,
            "route": f"{from_code}-{to_code}",
            "date": date_str
        }


# Global enhanced service instance
enhanced_flight_search = EnhancedFlightSearch()
