from pydantic import BaseModel, EmailStr
from enum import Enum

class UserRole(str, Enum):
    """
    Enum for user roles.
    """
    ADMIN = "admin"
    CUSTOMER = "customer"

class UserCreate(BaseModel):
    """
    Schema for creating a new user - matches YAML specification exactly.
    """
    email: EmailStr  # User's email must be valid
    name: str        # User's name
    phone_number: str  # User's phone number
    phone_country_code: str# User's phone country code
    role: UserRole

class UserOtpVerification(BaseModel):
    """
    Schema for verifying otp of user
    """
    email: str
    otp: str

class UserLogin(BaseModel):
    """
    Schema for user login - matches YAML specification exactly.
    """
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    """
    Schema for returning user information.
    """
    id: int               # User's unique ID
    email: EmailStr       # User's email
    name: str             # User's name
    phone_number: str     # User's phone number
    phone_country_code: str  # User's phone country code
    role: UserRole

    class Config:
        from_attributes = True  # Enable ORM mode for SQLAlchemy integration