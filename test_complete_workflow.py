import requests
import json

def test_complete_flight_booking_workflow():
    """Test the complete flight booking workflow with TripJack integration"""
    
    base_url = 'http://localhost:8000'
    headers = {'Content-Type': 'application/json'}
    
    print("🚀 Testing Complete Flight Booking Workflow with TripJack")
    print("=" * 60)
    
    # Step 1: Flight Search
    print("Step 1: Flight Search")
    search_payload = {
        'Cabin': 'E',
        'FareType': 'REGULAR',
        'ADT': 1,
        'CHD': 0,
        'INF': 0,
        'Trips': [
            {
                'From': 'DEL',
                'To': 'BOM',
                'OnwardDate': '2025-06-25'
            }
        ]
    }
    
    try:
        response = requests.post(f"{base_url}/apis/search", json=search_payload, headers=headers, timeout=90)
        print(f"✅ Search Status: {response.status_code}")
        search_result = response.json()
        
        if search_result.get('Trips') and search_result['Trips'][0].get('Journey'):
            journeys = search_result['Trips'][0]['Journey']
            print(f"✅ Found {len(journeys)} flights")
            
            # Get the first flight for pricing
            first_flight = journeys[0]
            flight_index = first_flight['Index']
            tui = search_result.get('TUI')
            
            print(f"✅ Selected flight: {first_flight['FlightNo']} ({first_flight['AirlineName']})")
            print(f"✅ Price: ₹{first_flight['GrossFare']}")
            print(f"✅ TUI: {tui}")
            
            # Step 2: Get Pricing Details
            print("\nStep 2: Get Pricing Details")
            pricing_payload = {
                'Trips': [
                    {
                        'Amount': first_flight['GrossFare'],
                        'Index': flight_index,
                        'OrderID': 1,
                        'TUI': tui
                    }
                ],
                'TripType': 'O'
            }
            
            response = requests.post(f"{base_url}/apis/pricing", json=pricing_payload, headers=headers, timeout=90)
            print(f"✅ Pricing Status: {response.status_code}")
            pricing_result = response.json()
            
            if pricing_result.get('Trips'):
                print(f"✅ Pricing confirmed: ₹{pricing_result['GrossAmount']}")
                
                # Step 3: Get Flight Details
                print("\nStep 3: Get Flight Details")
                details_payload = {
                    'Trips': [
                        {
                            'Amount': first_flight['GrossFare'],
                            'Index': flight_index,
                            'OrderID': 1,
                            'TUI': tui
                        }
                    ],
                    'TripType': 'O'
                }
                
                response = requests.post(f"{base_url}/apis/details/", json=details_payload, headers=headers, timeout=90)
                print(f"✅ Details Status: {response.status_code}")
                details_result = response.json()
                
                # Step 4: Get Fare Rules
                print("\nStep 4: Get Fare Rules")
                response = requests.post(f"{base_url}/apis/rules/", json=details_payload, headers=headers, timeout=90)
                print(f"✅ Rules Status: {response.status_code}")
                
                # Step 5: Payment Processing
                print("\nStep 5: Payment Processing")
                payment_payload = {"booking_id": f"booking_{tui}"}
                
                response = requests.post(f"{base_url}/b2capis/payments/v1/", json=payment_payload, headers=headers)
                print(f"✅ Payment Status: {response.status_code}")
                payment_result = response.json()
                
                if payment_result.get('status') == 'success':
                    print(f"✅ Payment ID: {payment_result['payment_id']}")
                    print(f"✅ Transaction ID: {payment_result['transaction_id']}")
                
                print("\n🎉 Complete Workflow Test: SUCCESS!")
                print("✅ Flight Search: Working with real TripJack data")
                print("✅ Pricing: Working with TripJack review API")
                print("✅ Details: Working")
                print("✅ Rules: Working")
                print("✅ Payment: Working")
                
                return True
            else:
                print("❌ Pricing failed")
                return False
        else:
            print("❌ No flights found in search")
            return False
            
    except Exception as e:
        print(f"❌ Error in workflow: {e}")
        return False

if __name__ == "__main__":
    test_complete_flight_booking_workflow()
