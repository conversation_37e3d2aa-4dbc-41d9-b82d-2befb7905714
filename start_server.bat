@echo off
echo ========================================
echo Fast Travel Backend - Local Network Server
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  No virtual environment found
    echo Using system Python installation
)

echo.
echo 🚀 Starting server for local network access...
echo.
echo 📱 The server will be accessible from other devices on your network
echo 🔒 Make sure Windows Firewall allows Python through port 8000
echo.

REM Start the server
python run_server_local_network.py

echo.
echo 👋 Server stopped
pause
