"""
Advanced database connection pool manager with performance optimization.
Implements connection pooling, query optimization, and database result caching.
"""

import asyncio
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
import hashlib
import json

import aiomysql
from aiomysql import Pool, Connection, Cursor
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from app.config import RedisCache


@dataclass
class QueryStats:
    """Statistics for database queries."""
    query_hash: str
    query_type: str  # SELECT, INSERT, UPDATE, DELETE
    execution_count: int = 0
    total_execution_time: float = 0.0
    avg_execution_time: float = 0.0
    min_execution_time: float = float('inf')
    max_execution_time: float = 0.0
    last_executed: Optional[float] = None
    cache_hits: int = 0
    cache_misses: int = 0


@dataclass
class ConnectionPoolConfig:
    """Configuration for database connection pool."""
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    database: str = "flight_booking"
    charset: str = "utf8mb4"
    
    # Pool settings
    minsize: int = 5
    maxsize: int = 20
    pool_recycle: int = 3600  # 1 hour
    pool_timeout: int = 30
    
    # Query cache settings
    enable_query_cache: bool = True
    query_cache_ttl: int = 300  # 5 minutes
    max_cached_queries: int = 1000


class DatabaseConnectionPool:
    """
    Advanced database connection pool with query optimization and caching.
    """
    
    def __init__(self, config: ConnectionPoolConfig):
        self.config = config
        self.async_pool: Optional[Pool] = None
        self.sync_engine = None
        self.sync_session_factory = None
        self.redis_client = RedisCache.connection()
        
        # Performance tracking
        self.query_stats: Dict[str, QueryStats] = {}
        self.pool_stats = {
            "total_connections_created": 0,
            "total_connections_closed": 0,
            "active_connections": 0,
            "total_queries": 0,
            "cached_queries": 0,
            "slow_queries": 0,
            "failed_queries": 0
        }
        
        # Query cache
        self.query_cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, float] = {}
        
        # Slow query threshold (in seconds)
        self.slow_query_threshold = 1.0
    
    async def initialize_async_pool(self) -> None:
        """Initialize async connection pool."""
        try:
            self.async_pool = await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                db=self.config.database,
                charset=self.config.charset,
                minsize=self.config.minsize,
                maxsize=self.config.maxsize,
                autocommit=True
            )
            print(f"Async database pool initialized: {self.config.minsize}-{self.config.maxsize} connections")
            
        except Exception as e:
            print(f"Failed to initialize async database pool: {str(e)}")
            raise
    
    def initialize_sync_pool(self) -> None:
        """Initialize synchronous connection pool."""
        try:
            # Create SQLAlchemy engine with connection pooling
            database_url = (
                f"mysql+pymysql://{self.config.user}:{self.config.password}@"
                f"{self.config.host}:{self.config.port}/{self.config.database}"
                f"?charset={self.config.charset}"
            )
            
            self.sync_engine = create_engine(
                database_url,
                poolclass=QueuePool,
                pool_size=self.config.minsize,
                max_overflow=self.config.maxsize - self.config.minsize,
                pool_recycle=self.config.pool_recycle,
                pool_timeout=self.config.pool_timeout,
                echo=False  # Set to True for SQL debugging
            )
            
            self.sync_session_factory = sessionmaker(bind=self.sync_engine)
            print(f"Sync database pool initialized with SQLAlchemy")
            
        except Exception as e:
            print(f"Failed to initialize sync database pool: {str(e)}")
            raise
    
    async def close_async_pool(self) -> None:
        """Close async connection pool."""
        if self.async_pool:
            self.async_pool.close()
            await self.async_pool.wait_closed()
            print("Async database pool closed")
    
    def close_sync_pool(self) -> None:
        """Close synchronous connection pool."""
        if self.sync_engine:
            self.sync_engine.dispose()
            print("Sync database pool closed")
    
    def _generate_query_hash(self, query: str, params: Optional[Dict[str, Any]] = None) -> str:
        """Generate hash for query caching."""
        query_data = {
            "query": query.strip(),
            "params": params or {}
        }
        serialized = json.dumps(query_data, sort_keys=True)
        return hashlib.sha256(serialized.encode()).hexdigest()[:16]
    
    def _get_query_type(self, query: str) -> str:
        """Determine query type from SQL statement."""
        query_upper = query.strip().upper()
        if query_upper.startswith('SELECT'):
            return 'SELECT'
        elif query_upper.startswith('INSERT'):
            return 'INSERT'
        elif query_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif query_upper.startswith('DELETE'):
            return 'DELETE'
        else:
            return 'OTHER'
    
    def _should_cache_query(self, query_type: str, execution_time: float) -> bool:
        """Determine if query result should be cached."""
        if not self.config.enable_query_cache:
            return False
        
        # Only cache SELECT queries that take more than 100ms
        return query_type == 'SELECT' and execution_time > 0.1
    
    def _get_cached_result(self, query_hash: str) -> Optional[Any]:
        """Get cached query result."""
        if query_hash in self.query_cache:
            timestamp = self.cache_timestamps.get(query_hash, 0)
            if time.time() - timestamp < self.config.query_cache_ttl:
                return self.query_cache[query_hash]
            else:
                # Remove expired cache entry
                del self.query_cache[query_hash]
                del self.cache_timestamps[query_hash]
        
        return None
    
    def _cache_result(self, query_hash: str, result: Any) -> None:
        """Cache query result."""
        if len(self.query_cache) >= self.config.max_cached_queries:
            # Remove oldest cache entry
            oldest_hash = min(self.cache_timestamps.keys(), 
                            key=lambda k: self.cache_timestamps[k])
            del self.query_cache[oldest_hash]
            del self.cache_timestamps[oldest_hash]
        
        self.query_cache[query_hash] = result
        self.cache_timestamps[query_hash] = time.time()
    
    def _update_query_stats(self, query_hash: str, query_type: str, execution_time: float, cached: bool = False) -> None:
        """Update query performance statistics."""
        if query_hash not in self.query_stats:
            self.query_stats[query_hash] = QueryStats(
                query_hash=query_hash,
                query_type=query_type
            )
        
        stats = self.query_stats[query_hash]
        stats.execution_count += 1
        stats.last_executed = time.time()
        
        if cached:
            stats.cache_hits += 1
        else:
            stats.cache_misses += 1
            stats.total_execution_time += execution_time
            stats.avg_execution_time = stats.total_execution_time / stats.cache_misses
            stats.min_execution_time = min(stats.min_execution_time, execution_time)
            stats.max_execution_time = max(stats.max_execution_time, execution_time)
        
        # Update pool stats
        self.pool_stats["total_queries"] += 1
        if cached:
            self.pool_stats["cached_queries"] += 1
        if execution_time > self.slow_query_threshold:
            self.pool_stats["slow_queries"] += 1
    
    @asynccontextmanager
    async def get_async_connection(self):
        """Get async database connection from pool."""
        if not self.async_pool:
            await self.initialize_async_pool()
        
        connection = None
        try:
            connection = await self.async_pool.acquire()
            self.pool_stats["active_connections"] += 1
            yield connection
        except Exception as e:
            self.pool_stats["failed_queries"] += 1
            raise
        finally:
            if connection:
                await self.async_pool.release(connection)
                self.pool_stats["active_connections"] -= 1
    
    @asynccontextmanager
    async def get_sync_session(self):
        """Get synchronous database session."""
        if not self.sync_session_factory:
            self.initialize_sync_pool()
        
        session = None
        try:
            session = self.sync_session_factory()
            yield session
        except Exception as e:
            if session:
                session.rollback()
            self.pool_stats["failed_queries"] += 1
            raise
        finally:
            if session:
                session.close()
    
    async def execute_async_query(
        self, 
        query: str, 
        params: Optional[Dict[str, Any]] = None,
        fetch_all: bool = True
    ) -> Union[List[Dict[str, Any]], Dict[str, Any], None]:
        """
        Execute async query with caching and performance tracking.
        
        Args:
            query: SQL query string
            params: Query parameters
            fetch_all: Whether to fetch all results or just one
            
        Returns:
            Query results
        """
        start_time = time.time()
        query_hash = self._generate_query_hash(query, params)
        query_type = self._get_query_type(query)
        
        # Check cache for SELECT queries
        if query_type == 'SELECT':
            cached_result = self._get_cached_result(query_hash)
            if cached_result is not None:
                self._update_query_stats(query_hash, query_type, 0, cached=True)
                return cached_result
        
        # Execute query
        async with self.get_async_connection() as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(query, params)
                
                if query_type == 'SELECT':
                    if fetch_all:
                        result = await cursor.fetchall()
                    else:
                        result = await cursor.fetchone()
                else:
                    result = cursor.rowcount
        
        execution_time = time.time() - start_time
        self._update_query_stats(query_hash, query_type, execution_time)
        
        # Cache result if appropriate
        if self._should_cache_query(query_type, execution_time):
            self._cache_result(query_hash, result)
        
        return result
    
    def execute_sync_query(
        self, 
        query: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> Any:
        """
        Execute synchronous query with performance tracking.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Query results
        """
        start_time = time.time()
        query_hash = self._generate_query_hash(query, params)
        query_type = self._get_query_type(query)
        
        with self.get_sync_session() as session:
            result = session.execute(text(query), params or {})
            
            if query_type == 'SELECT':
                result = result.fetchall()
            else:
                session.commit()
                result = result.rowcount
        
        execution_time = time.time() - start_time
        self._update_query_stats(query_hash, query_type, execution_time)
        
        return result
    
    async def execute_batch_async(
        self, 
        queries: List[Tuple[str, Optional[Dict[str, Any]]]]
    ) -> List[Any]:
        """
        Execute multiple queries in a batch for better performance.
        
        Args:
            queries: List of (query, params) tuples
            
        Returns:
            List of query results
        """
        results = []
        
        async with self.get_async_connection() as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                for query, params in queries:
                    start_time = time.time()
                    query_hash = self._generate_query_hash(query, params)
                    query_type = self._get_query_type(query)
                    
                    await cursor.execute(query, params)
                    
                    if query_type == 'SELECT':
                        result = await cursor.fetchall()
                    else:
                        result = cursor.rowcount
                    
                    results.append(result)
                    
                    execution_time = time.time() - start_time
                    self._update_query_stats(query_hash, query_type, execution_time)
        
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get database performance statistics."""
        # Calculate aggregate stats
        total_queries = sum(stats.execution_count for stats in self.query_stats.values())
        total_cache_hits = sum(stats.cache_hits for stats in self.query_stats.values())
        
        slow_queries = [
            {
                "query_hash": stats.query_hash,
                "query_type": stats.query_type,
                "avg_execution_time": stats.avg_execution_time,
                "execution_count": stats.execution_count
            }
            for stats in self.query_stats.values()
            if stats.avg_execution_time > self.slow_query_threshold
        ]
        
        return {
            "pool_stats": self.pool_stats,
            "query_stats": {
                "total_unique_queries": len(self.query_stats),
                "total_query_executions": total_queries,
                "cache_hit_rate": total_cache_hits / total_queries if total_queries > 0 else 0,
                "cached_queries_count": len(self.query_cache),
                "slow_queries_count": len(slow_queries),
                "slow_queries": slow_queries[:10]  # Top 10 slow queries
            },
            "pool_config": {
                "minsize": self.config.minsize,
                "maxsize": self.config.maxsize,
                "query_cache_enabled": self.config.enable_query_cache,
                "query_cache_ttl": self.config.query_cache_ttl
            }
        }
    
    async def cleanup_expired_cache(self) -> int:
        """Clean up expired cache entries."""
        current_time = time.time()
        expired_keys = []
        
        for query_hash, timestamp in self.cache_timestamps.items():
            if current_time - timestamp > self.config.query_cache_ttl:
                expired_keys.append(query_hash)
        
        for key in expired_keys:
            del self.query_cache[key]
            del self.cache_timestamps[key]
        
        return len(expired_keys)


# Global database connection pool instance
db_pool_config = ConnectionPoolConfig()
db_connection_pool = DatabaseConnectionPool(db_pool_config)
