from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.microservices.flight_service.detail_service.service import FlightDetail
from app.microservices.flight_service.schemas import DetailsRequest

# Create an instance of APIRouter
router = APIRouter()
flight_detail = FlightDetail()

@router.post("/details/")
async def get_flight_details(request: DetailsRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to get detailed flight information - matches YAML specification exactly.

    Args:
        request (DetailsRequest): A request containing detailed flight parameters,
                                 including Trips, ClientID, Mode, Options, Source, TripType

    Background Tasks:
        Allows for background processing of tasks if needed.

    Returns:
        dict: The flight details or an error response.
    """
    try:
        # Call the detail method from FlightDetail service
        request_data = request.model_dump()
        result = flight_detail.detail(request_data)
        return result
    except HTTPException as e:
        # If an HTTP exception occurs, raise it to provide an appropriate error response
        raise e
    except Exception as e:
        # Handle any other exceptions that may arise
        raise HTTPException(status_code=500, detail=str(e))
