#!/usr/bin/env python3
"""
Test script for Phase 3 implementation verification.
Tests database optimization, performance analytics, and monitoring capabilities.
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any, List


class Phase3Tester:
    """Test suite for Phase 3 database and analytics implementation."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None
        self.test_results = []
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request to the API."""
        url = f"{self.base_url}/apis{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await response.json()
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    async def test_database_connection_pool(self):
        """Test database connection pool functionality."""
        print("\n🗄️ Testing Database Connection Pool...")
        
        # Initialize database pools
        init_result = await self.make_request("POST", "/admin/database/initialize-pools")
        init_success = init_result.get("status") == "success"
        
        self.log_test_result(
            "Database Pool Initialization",
            init_success,
            init_result.get("message", "Unknown result")
        )
        
        # Check pool health
        health_result = await self.make_request("GET", "/admin/database/connection-pool/health")
        health_status = health_result.get("health_status", "unknown")
        
        self.log_test_result(
            "Database Pool Health",
            health_status in ["healthy", "warning"],
            f"Status: {health_status}, Active connections: {health_result.get('active_connections', 0)}"
        )
        
        # Get database statistics
        stats_result = await self.make_request("GET", "/admin/database/stats")
        has_stats = "database_stats" in stats_result
        
        self.log_test_result(
            "Database Statistics",
            has_stats,
            f"Pool stats available: {has_stats}"
        )
    
    async def test_optimized_booking_operations(self):
        """Test optimized booking service operations."""
        print("\n📋 Testing Optimized Booking Operations...")
        
        # Test optimized booking creation
        booking_request = {
            "booking_data": {
                "user_id": "test_user_123",
                "total_amount": 15000,
                "currency": "INR"
            },
            "flight_details": {
                "flight_id": "FL123456",
                "fare_id": "FARE789",
                "departure_date": "2024-02-15",
                "cabin_class": "E",
                "fare_type": "REGULAR"
            },
            "travellers": [
                {
                    "title": "Mr",
                    "first_name": "John",
                    "last_name": "Doe",
                    "date_of_birth": "1990-01-01",
                    "gender": "M",
                    "nationality": "IN"
                }
            ],
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+91-9876543210",
                "address": "123 Test Street",
                "city": "Mumbai",
                "country": "India"
            }
        }
        
        start_time = time.time()
        create_result = await self.make_request("POST", "/admin/booking/create-optimized", booking_request)
        creation_time = time.time() - start_time
        
        booking_created = create_result.get("status") == "success"
        booking_id = None
        
        if booking_created:
            booking_data = create_result.get("booking", {})
            booking_id = booking_data.get("booking_id")
            creation_time_ms = booking_data.get("creation_time_ms", creation_time * 1000)
            
            self.log_test_result(
                "Optimized Booking Creation",
                True,
                f"Created in {creation_time_ms:.2f}ms, ID: {booking_id}"
            )
        else:
            self.log_test_result(
                "Optimized Booking Creation",
                False,
                create_result.get("detail", "Creation failed")
            )
        
        # Test optimized booking retrieval
        if booking_id:
            retrieval_request = {
                "booking_id": booking_id,
                "user_id": "test_user_123"
            }
            
            start_time = time.time()
            retrieve_result = await self.make_request("POST", "/admin/booking/get-optimized", retrieval_request)
            retrieval_time = time.time() - start_time
            
            booking_retrieved = retrieve_result.get("status") == "success"
            
            if booking_retrieved:
                booking_data = retrieve_result.get("booking", {})
                retrieval_time_ms = booking_data.get("retrieval_time_ms", retrieval_time * 1000)
                cache_hit = booking_data.get("cache_hit", False)
                
                self.log_test_result(
                    "Optimized Booking Retrieval",
                    True,
                    f"Retrieved in {retrieval_time_ms:.2f}ms, Cache hit: {cache_hit}"
                )
            else:
                self.log_test_result(
                    "Optimized Booking Retrieval",
                    False,
                    retrieve_result.get("detail", "Retrieval failed")
                )
        
        # Test booking service statistics
        service_stats = await self.make_request("GET", "/admin/booking/service-stats")
        has_service_stats = "booking_service_stats" in service_stats
        
        if has_service_stats:
            stats = service_stats["booking_service_stats"]
            cache_hit_rate = stats.get("cache_hit_rate", 0)
            total_operations = stats.get("total_operations", 0)
            
            self.log_test_result(
                "Booking Service Statistics",
                True,
                f"Operations: {total_operations}, Cache hit rate: {cache_hit_rate:.2%}"
            )
        else:
            self.log_test_result(
                "Booking Service Statistics",
                False,
                "Service stats not available"
            )
    
    async def test_performance_analytics(self):
        """Test performance analytics and monitoring."""
        print("\n📊 Testing Performance Analytics...")
        
        # Record custom metrics
        metric_requests = [
            {"metric_type": "test_response_time", "value": 250, "service": "test_service"},
            {"metric_type": "test_cache_hit_rate", "value": 0.85, "service": "test_service"},
            {"metric_type": "test_error_rate", "value": 0.02, "service": "test_service"}
        ]
        
        metrics_recorded = 0
        for metric_req in metric_requests:
            result = await self.make_request(
                "POST", 
                f"/admin/analytics/record-metric?metric_type={metric_req['metric_type']}&value={metric_req['value']}&service={metric_req['service']}"
            )
            if result.get("status") == "success":
                metrics_recorded += 1
        
        self.log_test_result(
            "Custom Metrics Recording",
            metrics_recorded == len(metric_requests),
            f"Recorded {metrics_recorded}/{len(metric_requests)} metrics"
        )
        
        # Get analytics dashboard
        dashboard_result = await self.make_request("GET", "/admin/analytics/dashboard")
        has_dashboard = "dashboard" in dashboard_result
        
        if has_dashboard:
            dashboard = dashboard_result["dashboard"]
            system_health = dashboard.get("system_health", {})
            current_metrics = dashboard.get("current_metrics", {})
            
            self.log_test_result(
                "Performance Dashboard",
                True,
                f"Health: {system_health.get('overall_status', 'unknown')}, Metrics: {len(current_metrics)}"
            )
        else:
            self.log_test_result(
                "Performance Dashboard",
                False,
                "Dashboard not available"
            )
        
        # Generate performance report
        report_request = {"hours": 1}
        report_result = await self.make_request("POST", "/admin/analytics/report", report_request)
        has_report = "report" in report_result
        
        if has_report:
            report = report_result["report"]
            services_count = len(report.get("services", {}))
            
            self.log_test_result(
                "Performance Report Generation",
                True,
                f"Report generated for {services_count} services"
            )
        else:
            self.log_test_result(
                "Performance Report Generation",
                False,
                "Report generation failed"
            )
        
        # Get analytics statistics
        analytics_stats = await self.make_request("GET", "/admin/analytics/stats")
        has_analytics_stats = "analytics_stats" in analytics_stats
        
        if has_analytics_stats:
            stats = analytics_stats["analytics_stats"]
            total_metrics = stats.get("total_metrics_collected", 0)
            
            self.log_test_result(
                "Analytics Statistics",
                True,
                f"Total metrics collected: {total_metrics}"
            )
        else:
            self.log_test_result(
                "Analytics Statistics",
                False,
                "Analytics stats not available"
            )
    
    async def test_system_health_monitoring(self):
        """Test system health monitoring capabilities."""
        print("\n🏥 Testing System Health Monitoring...")
        
        # Get system health
        health_result = await self.make_request("GET", "/admin/performance/system-health")
        has_health = "system_health" in health_result
        
        if has_health:
            system_health = health_result["system_health"]
            overall_status = system_health.get("overall_status", "unknown")
            overall_score = system_health.get("overall_score", 0)
            component_health = system_health.get("component_health", {})
            
            self.log_test_result(
                "System Health Monitoring",
                overall_status in ["healthy", "warning", "critical"],
                f"Status: {overall_status}, Score: {overall_score:.2f}, Components: {len(component_health)}"
            )
            
            # Log component health details
            for component, health_data in component_health.items():
                component_status = health_data.get("status", "unknown")
                component_score = health_data.get("score", 0)
                print(f"  - {component}: {component_status} (score: {component_score:.2f})")
        else:
            self.log_test_result(
                "System Health Monitoring",
                False,
                "System health not available"
            )
    
    async def test_database_query_optimization(self):
        """Test database query execution and optimization."""
        print("\n🔍 Testing Database Query Optimization...")
        
        # Test simple query execution
        query_request = {
            "query": "SELECT 1 as test_value",
            "params": {},
            "fetch_all": True
        }
        
        start_time = time.time()
        query_result = await self.make_request("POST", "/admin/database/query", query_request)
        query_time = time.time() - start_time
        
        query_success = query_result.get("status") == "success"
        
        if query_success:
            execution_time_ms = query_result.get("execution_time_ms", query_time * 1000)
            query_type = query_result.get("query_type", "unknown")
            
            self.log_test_result(
                "Database Query Execution",
                True,
                f"Executed in {execution_time_ms:.2f}ms, Type: {query_type}"
            )
        else:
            self.log_test_result(
                "Database Query Execution",
                False,
                query_result.get("detail", "Query execution failed")
            )
        
        # Test cache cleanup
        cleanup_result = await self.make_request("POST", "/admin/database/cleanup-cache")
        cleanup_success = cleanup_result.get("status") == "success"
        
        if cleanup_success:
            cleaned_entries = cleanup_result.get("cleaned_entries", 0)
            
            self.log_test_result(
                "Database Cache Cleanup",
                True,
                f"Cleaned {cleaned_entries} expired entries"
            )
        else:
            self.log_test_result(
                "Database Cache Cleanup",
                False,
                cleanup_result.get("detail", "Cleanup failed")
            )
    
    async def run_all_tests(self):
        """Run all Phase 3 tests."""
        print("🚀 Starting Phase 3 Implementation Tests...\n")
        
        # Run tests
        await self.test_database_connection_pool()
        await self.test_optimized_booking_operations()
        await self.test_performance_analytics()
        await self.test_system_health_monitoring()
        await self.test_database_query_optimization()
        
        # Summary
        print("\n📊 Test Summary:")
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 All tests passed! Phase 3 implementation is working correctly.")
        elif passed >= total * 0.8:
            print("⚠️  Most tests passed. Some features may need attention.")
        else:
            print("❌ Several tests failed. Phase 3 implementation needs review.")
        
        # Detailed results
        print("\n📋 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test']}: {result['details']}")
        
        return passed, total


async def main():
    """Main test function."""
    async with Phase3Tester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
