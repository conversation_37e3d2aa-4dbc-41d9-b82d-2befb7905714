# Flight Booking Optimization System - API Documentation Guide

## Overview

This guide provides comprehensive information about the Flight Booking Optimization System API documentation, including how to use the OpenAPI specification, explore the interactive documentation, and integrate with the system.

## 📋 Documentation Files

### Primary Documentation
- **`openapi_specification.yaml`** - Complete OpenAPI 3.0 specification
- **`API_DOCUMENTATION_GUIDE.md`** - This guide (usage instructions)

### Implementation Summaries
- **`PHASE_1_IMPLEMENTATION_SUMMARY.md`** - Caching infrastructure details
- **`PHASE_2_IMPLEMENTATION_SUMMARY.md`** - Async processing features
- **`PHASE_3_IMPLEMENTATION_SUMMARY.md`** - Database optimization
- **`PHASE_4_IMPLEMENTATION_SUMMARY.md`** - Advanced monitoring
- **`COMPLETE_PROJECT_SUMMARY.md`** - Overall project overview

## 🚀 Getting Started with the API Documentation

### 1. Viewing the Interactive Documentation

#### Option A: Swagger UI (Recommended)
```bash
# Install swagger-ui-serve globally
npm install -g swagger-ui-serve

# Serve the documentation
swagger-ui-serve openapi_specification.yaml

# Open browser to http://localhost:3000
```

#### Option B: Redoc
```bash
# Install redoc-cli globally
npm install -g redoc-cli

# Generate static HTML documentation
redoc-cli build openapi_specification.yaml --output api-docs.html

# Open api-docs.html in your browser
```

#### Option C: Online Swagger Editor
1. Go to https://editor.swagger.io/
2. Copy the contents of `openapi_specification.yaml`
3. Paste into the editor for interactive exploration

### 2. API Testing and Exploration

#### Using the Interactive "Try it out" Feature
1. Open the Swagger UI documentation
2. Click on any endpoint to expand it
3. Click "Try it out" button
4. Fill in the required parameters
5. Click "Execute" to make a real API call

#### Example: Testing Flight Search
```yaml
# In Swagger UI, navigate to:
# Flight Search > POST /flight-search > Try it out

# Example request body:
{
  "Trips": [
    {
      "From": "DEL",
      "To": "BOM", 
      "OnwardDate": "2024-02-15"
    }
  ],
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "E",
  "FareType": "REGULAR"
}
```

## 🏗️ API Architecture Overview

### Service Categories (115+ Endpoints)

#### 1. Core Flight Services (50+ endpoints)
**Purpose**: Essential flight booking operations
**Key Features**:
- Flight search with intelligent caching
- Real-time pricing and availability
- Comprehensive booking management
- SSR (Special Service Requests) handling
- Fare rules and restrictions

**Main Endpoints**:
- `POST /flight-search` - Search for flights
- `POST /flight-pricing` - Get detailed pricing
- `POST /booking/create` - Create new booking
- `GET /ssr/services` - Available special services
- `GET /fare-rules/{fareId}` - Fare rules and restrictions

#### 2. Caching Management (15+ endpoints)
**Purpose**: Multi-layer cache optimization (Admin Access Required)
**Key Features**:
- L1 Memory + L2 Redis + L3 Persistent caching
- Intelligent cache warming
- Real-time performance monitoring
- Cache health diagnostics

**Main Endpoints**:
- `GET /cache/statistics` - Cache performance metrics
- `POST /cache/warm` - Trigger cache warming
- `POST /cache/invalidate` - Invalidate cache entries
- `GET /cache/health` - Cache system health

#### 3. Async Operations (20+ endpoints)
**Purpose**: Enhanced async services with optimization
**Key Features**:
- Circuit breaker protection
- Request deduplication (60-80% reduction)
- Intelligent retry logic
- Real-time performance monitoring

**Main Endpoints**:
- `POST /async/search` - Enhanced async flight search
- `POST /async/pricing` - Enhanced async pricing
- `GET /performance/search` - Search service metrics
- `GET /performance/provider` - Provider performance

#### 4. Database & Analytics (15+ endpoints)
**Purpose**: Optimized data operations (Admin Access Required)
**Key Features**:
- Advanced connection pooling
- Query optimization and caching
- Batch operations for performance
- Comprehensive analytics

**Main Endpoints**:
- `GET /admin/database/stats` - Database performance
- `POST /admin/booking/create-optimized` - Optimized booking
- `GET /admin/analytics/dashboard` - Performance dashboard
- `POST /admin/database/query` - Execute optimized queries

#### 5. Advanced Monitoring (15+ endpoints)
**Purpose**: Real-time monitoring and analytics (Admin Access Required)
**Key Features**:
- WebSocket real-time dashboards
- Automated report generation
- ML-based predictive analytics
- Intelligent auto-scaling

**Main Endpoints**:
- `WS /advanced/dashboard/realtime` - WebSocket dashboard
- `POST /advanced/reporting/generate-report` - Generate reports
- `POST /advanced/analytics/forecast` - Predictive forecasting
- `GET /advanced/monitoring/comprehensive-status` - System status

## 🔐 Authentication & Security

### API Key Authentication
```http
X-API-Key: your-api-key-here
```

**Rate Limits**:
- **Standard**: 1000 requests/hour
- **Premium**: 10000 requests/hour  
- **Enterprise**: Unlimited

### JWT Bearer Token Authentication
```http
Authorization: Bearer your-jwt-token-here
```

**Permissions**:
- `flight:read` - Access to flight search and details
- `booking:write` - Create and modify bookings
- `admin:read` - Access to monitoring and analytics
- `admin:write` - Administrative operations

### Obtaining API Credentials
1. Register at https://portal.flightbooking.com
2. Generate API keys in developer dashboard
3. Configure rate limits and permissions
4. Test with sandbox environment first

## 📊 Performance Features

### Multi-layer Caching
- **L1 Memory Cache**: Sub-millisecond access (95%+ hit rate)
- **L2 Redis Cache**: 1-5ms access with persistence
- **L3 Persistent Cache**: 5-50ms for stable data

### Optimization Headers
```http
# Response headers indicate optimization status
X-Cache-Status: HIT|MISS|REFRESH
X-Response-Time: 45
X-Circuit-Breaker-Status: CLOSED|OPEN|HALF_OPEN
X-Request-Deduplication: true|false
X-Performance-Score: 95
```

### Circuit Breaker Protection
- **CLOSED**: Normal operation
- **OPEN**: Service issues, serving cached data
- **HALF_OPEN**: Testing service recovery

## 🧪 Testing Examples

### 1. Basic Flight Search
```bash
curl -X POST "https://api.flightbooking.com/v4/flight-search" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-02-15"}],
    "ADT": 1, "CHD": 0, "INF": 0,
    "Cabin": "E", "FareType": "REGULAR"
  }'
```

### 2. Enhanced Async Search
```bash
curl -X POST "https://api.flightbooking.com/v4/async/search" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-02-15"}],
    "ADT": 1, "CHD": 0, "INF": 0,
    "Cabin": "E", "FareType": "REGULAR",
    "async": true
  }'
```

### 3. Cache Performance Monitoring
```bash
curl -X GET "https://api.flightbooking.com/v4/cache/statistics" \
  -H "X-API-Key: your-api-key"
```

### 4. Real-time Dashboard (WebSocket)
```javascript
const ws = new WebSocket('wss://api.flightbooking.com/v4/advanced/dashboard/realtime');

ws.onopen = function() {
  // Subscribe to system metrics
  ws.send(JSON.stringify({
    type: 'subscribe',
    subscription: 'system_metrics'
  }));
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Real-time metric:', data);
};
```

### 5. Predictive Analytics
```bash
curl -X POST "https://api.flightbooking.com/v4/advanced/analytics/forecast" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "metric_name": "response_times",
    "hours_ahead": 24
  }'
```

## 🔧 Environment Configuration

### Server URLs
- **Production**: `https://api.flightbooking.com/v4`
- **Staging**: `https://staging-api.flightbooking.com/v4`
- **Development**: `https://dev-api.flightbooking.com/v4`
- **Local**: `http://localhost:8000/apis`

### Request/Response Format
- **Content-Type**: `application/json`
- **Character Encoding**: UTF-8
- **Date Format**: ISO 8601 (`YYYY-MM-DDTHH:mm:ssZ`)
- **Currency**: ISO 4217 3-letter codes

## 📚 Advanced Features

### WebSocket Real-time Dashboard
**Connection**: `wss://api.flightbooking.com/v4/advanced/dashboard/realtime`

**Subscription Types**:
- `system_metrics` - Overall system performance
- `performance_alerts` - Real-time alerts
- `cache_statistics` - Cache performance
- `database_metrics` - Database performance
- `booking_analytics` - Booking system metrics

### Automated Reporting
**Report Types**:
- `daily_summary` - Daily performance overview
- `weekly_performance` - Weekly trend analysis
- `system_health` - Current system status
- `alert_digest` - Alert summaries

**Output Formats**:
- `json` - Structured data
- `html` - Rich formatted reports
- `csv` - Tabular data export
- `pdf` - Printable documents

### Predictive Analytics
**Supported Metrics**:
- `response_times` - API response forecasting
- `cache_hit_rates` - Cache performance prediction
- `request_volumes` - Traffic forecasting
- `error_rates` - Error trend prediction

**ML Capabilities**:
- 24-hour forecasting with 85%+ accuracy
- Anomaly detection with statistical analysis
- Confidence intervals and uncertainty quantification
- Capacity planning recommendations

## 🚨 Error Handling

### Standard Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": "Required field 'From' is missing",
    "field_errors": [
      {
        "field": "From",
        "message": "This field is required",
        "code": "REQUIRED"
      }
    ]
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR` - Invalid input parameters
- `MISSING_API_KEY` - API key required
- `INVALID_API_KEY` - Invalid or revoked API key
- `RATE_LIMIT_EXCEEDED` - Rate limit exceeded
- `TUI_NOT_FOUND` - Transaction ID not found
- `CIRCUIT_BREAKER_OPEN` - Service temporarily unavailable
- `INTERNAL_SERVER_ERROR` - Unexpected server error

### Rate Limiting Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1642251600
Retry-After: 3600
```

## 📞 Support & Resources

### Documentation Resources
- **API Reference**: Interactive Swagger UI documentation
- **Implementation Guides**: Phase-specific implementation details
- **Code Examples**: Sample requests and responses
- **SDKs**: Available for Python, JavaScript, Java, .NET

### Support Channels
- **Technical Support**: <EMAIL>
- **Documentation**: https://docs.flightbooking.com
- **Developer Portal**: https://portal.flightbooking.com
- **Status Page**: https://status.flightbooking.com

### Best Practices
1. **Use appropriate caching headers** for optimal performance
2. **Implement retry logic** with exponential backoff
3. **Monitor rate limits** and implement proper throttling
4. **Handle circuit breaker states** gracefully
5. **Use WebSocket connections** for real-time monitoring
6. **Leverage predictive analytics** for capacity planning

## 🎯 Next Steps

1. **Explore the Interactive Documentation** using Swagger UI
2. **Test Basic Endpoints** with your API key
3. **Implement Error Handling** following the standard format
4. **Set Up Monitoring** using the real-time dashboard
5. **Optimize Performance** with caching and async features
6. **Scale Your Integration** using predictive analytics

The Flight Booking Optimization System API provides enterprise-grade performance, reliability, and intelligence for modern flight booking applications. Start with the basic endpoints and gradually explore the advanced optimization features to maximize your application's performance.
