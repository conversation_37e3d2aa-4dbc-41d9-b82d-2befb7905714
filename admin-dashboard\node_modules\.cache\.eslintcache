[{"C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Cache.tsx": "4", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Dashboard.tsx": "5", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Performance.tsx": "6", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Health.tsx": "7", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Searches.tsx": "8", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Analytics.tsx": "9", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Settings.tsx": "10", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Errors.tsx": "11", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Alerts.tsx": "12", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Export.tsx": "13", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\TripJack.tsx": "14", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Users.tsx": "15", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\NotFound.tsx": "16", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\contexts\\AppContext.tsx": "17", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Layout.tsx": "18", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useWebSocket.ts": "19", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useApi.ts": "20", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Dashboard\\MetricsCard.tsx": "21", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\websocket.ts": "22", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Sidebar.tsx": "23", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Header.tsx": "24", "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\api.ts": "25"}, {"size": 982, "mtime": 1748262223389, "results": "26", "hashOfConfig": "27"}, {"size": 425, "mtime": 1748262231583, "results": "28", "hashOfConfig": "27"}, {"size": 2261, "mtime": 1748263283719, "results": "29", "hashOfConfig": "27"}, {"size": 492, "mtime": 1748262246328, "results": "30", "hashOfConfig": "27"}, {"size": 14021, "mtime": 1748263299479, "results": "31", "hashOfConfig": "27"}, {"size": 516, "mtime": 1748262239387, "results": "32", "hashOfConfig": "27"}, {"size": 488, "mtime": 1748262279502, "results": "33", "hashOfConfig": "27"}, {"size": 496, "mtime": 1748262252696, "results": "34", "hashOfConfig": "27"}, {"size": 498, "mtime": 1748262258888, "results": "35", "hashOfConfig": "27"}, {"size": 492, "mtime": 1748262272658, "results": "36", "hashOfConfig": "27"}, {"size": 482, "mtime": 1748262266073, "results": "37", "hashOfConfig": "27"}, {"size": 488, "mtime": 1748262294411, "results": "38", "hashOfConfig": "27"}, {"size": 484, "mtime": 1748262308425, "results": "39", "hashOfConfig": "27"}, {"size": 506, "mtime": 1748262286713, "results": "40", "hashOfConfig": "27"}, {"size": 490, "mtime": 1748262301190, "results": "41", "hashOfConfig": "27"}, {"size": 934, "mtime": 1748262316748, "results": "42", "hashOfConfig": "27"}, {"size": 9452, "mtime": 1748263396894, "results": "43", "hashOfConfig": "27"}, {"size": 5438, "mtime": 1748263315036, "results": "44", "hashOfConfig": "27"}, {"size": 6491, "mtime": 1748263385943, "results": "45", "hashOfConfig": "27"}, {"size": 5221, "mtime": 1748263374530, "results": "46", "hashOfConfig": "27"}, {"size": 7089, "mtime": 1748263833676, "results": "47", "hashOfConfig": "27"}, {"size": 5900, "mtime": 1748263361985, "results": "48", "hashOfConfig": "27"}, {"size": 10351, "mtime": 1748263906538, "results": "49", "hashOfConfig": "27"}, {"size": 9288, "mtime": 1748263326234, "results": "50", "hashOfConfig": "27"}, {"size": 7909, "mtime": 1748263351162, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kxlmuo", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Cache.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Dashboard.tsx", ["127", "128", "129", "130", "131", "132", "133"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Performance.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Health.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Searches.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Analytics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Settings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Errors.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Alerts.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Export.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\TripJack.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\Users.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\pages\\NotFound.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\contexts\\AppContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useWebSocket.ts", ["134", "135", "136"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\hooks\\useApi.ts", ["137"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Dashboard\\MetricsCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\websocket.ts", ["138"], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\PROJECTS\\fast_travel_backend\\admin-dashboard\\src\\services\\api.ts", [], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 22, "column": 17, "nodeType": "141", "messageId": "142", "endLine": 22, "endColumn": 31}, {"ruleId": "139", "severity": 1, "message": "143", "line": 28, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 28, "endColumn": 12}, {"ruleId": "139", "severity": 1, "message": "144", "line": 29, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 29, "endColumn": 7}, {"ruleId": "139", "severity": 1, "message": "145", "line": 51, "column": 17, "nodeType": "141", "messageId": "142", "endLine": 51, "endColumn": 32}, {"ruleId": "139", "severity": 1, "message": "146", "line": 52, "column": 17, "nodeType": "141", "messageId": "142", "endLine": 52, "endColumn": 26}, {"ruleId": "139", "severity": 1, "message": "147", "line": 53, "column": 17, "nodeType": "141", "messageId": "142", "endLine": 53, "endColumn": 27}, {"ruleId": "139", "severity": 1, "message": "148", "line": 53, "column": 38, "nodeType": "141", "messageId": "142", "endLine": 53, "endColumn": 51}, {"ruleId": "149", "severity": 1, "message": "150", "line": 63, "column": 19, "nodeType": "141", "endLine": 63, "endColumn": 26}, {"ruleId": "149", "severity": 1, "message": "151", "line": 114, "column": 6, "nodeType": "152", "endLine": 114, "endColumn": 30, "suggestions": "153"}, {"ruleId": "149", "severity": 1, "message": "154", "line": 114, "column": 14, "nodeType": "155", "endLine": 114, "endColumn": 29}, {"ruleId": "149", "severity": 1, "message": "154", "line": 64, "column": 27, "nodeType": "155", "endLine": 64, "endColumn": 42}, {"ruleId": "139", "severity": 1, "message": "156", "line": 2, "column": 10, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 26}, "@typescript-eslint/no-unused-vars", "'TrendingUpIcon' is defined but never used.", "Identifier", "unusedVar", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "'performanceData' is assigned a value but never used.", "'cacheData' is assigned a value but never used.", "'healthData' is assigned a value but never used.", "'healthLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "The ref value 'handlersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'handlersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'handler'. Either include it or remove the dependency array. If 'handler' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["157"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", "'WebSocketMessage' is defined but never used.", {"desc": "158", "fix": "159"}, "Update the dependencies array to be: [event, handler]", {"range": "160", "text": "161"}, [3177, 3201], "[event, handler]"]