"""
Optimized flight search routes designed for sub-3-second response times.
Implements async processing, intelligent caching, and performance monitoring.
"""

import asyncio
import time
from fastapi import APIRouter, HTTPException, BackgroundTasks, Request
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from typing import Dict, Any

from app.microservices.flight_service.schemas import FlightSearchRequest, SearchListRequest
from app.microservices.flight_service.search_service.optimized_service import optimized_flight_search
from app.microservices.flight_service.analytics.performance_service import performance_analytics
from app.microservices.flight_service import config

# Create router instance
router = APIRouter()


@router.post("/search/optimized")
async def optimized_search(
    request: FlightSearchRequest, 
    background_tasks: BackgroundTasks,
    http_request: Request
):
    """
    Optimized flight search endpoint designed for sub-3-second response times.
    
    Features:
    - Async processing with intelligent fallback
    - Multi-layer caching with LRU eviction
    - Request deduplication
    - Performance monitoring and alerting
    - Automatic cache warming
    
    Args:
        request: Flight search request parameters
        background_tasks: FastAPI background tasks
        http_request: HTTP request object for metadata
        
    Returns:
        Flight search results with performance metadata
    """
    start_time = time.time()
    request_data = request.model_dump()
    
    # Add request metadata for tracking
    request_metadata = {
        "client_ip": http_request.client.host if http_request.client else "unknown",
        "user_agent": http_request.headers.get("user-agent", "unknown"),
        "request_id": http_request.headers.get("x-request-id", f"req_{int(time.time() * 1000)}")
    }
    
    try:
        # Execute optimized search
        result = await optimized_flight_search.search_async(request_data)
        
        # Record performance metrics
        response_time = time.time() - start_time
        response_time_ms = round(response_time * 1000, 2)
        
        # Record metrics for analytics
        await performance_analytics.record_metric(
            "search_response_time",
            response_time_ms,
            "optimized_search",
            {
                "cache_hit": result.get("cache_hit", False),
                "data_source": result.get("data_source", "unknown"),
                "performance_target_met": response_time_ms <= 3000,
                **request_metadata
            }
        )
        
        # Add performance headers
        headers = {
            "X-Response-Time": str(response_time_ms),
            "X-Cache-Hit": str(result.get("cache_hit", False)),
            "X-Data-Source": result.get("data_source", "unknown"),
            "X-Performance-Target-Met": str(response_time_ms <= 3000),
            "X-Request-ID": request_metadata["request_id"]
        }
        
        # Check if response time exceeds target
        if response_time_ms > config.performance_config["response_time_target_ms"]:
            # Trigger background optimization
            background_tasks.add_task(
                _trigger_performance_optimization,
                request_data,
                response_time_ms,
                result.get("data_source", "unknown")
            )
        
        return JSONResponse(content=result, headers=headers)
        
    except Exception as e:
        # Record error metrics
        error_response_time = round((time.time() - start_time) * 1000, 2)
        
        await performance_analytics.record_metric(
            "search_error_rate",
            1.0,
            "optimized_search",
            {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "response_time_ms": error_response_time,
                **request_metadata
            }
        )
        
        # Return error response with performance metadata
        error_response = {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Search failed: {str(e)}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Internal server error occurred"],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": error_response_time,
            "data_source": "error",
            "error_details": str(e),
            "performance_target_met": False
        }
        
        headers = {
            "X-Response-Time": str(error_response_time),
            "X-Error": "true",
            "X-Request-ID": request_metadata["request_id"]
        }
        
        return JSONResponse(content=error_response, status_code=500, headers=headers)


@router.post("/search/list/optimized")
async def optimized_search_list(
    request: SearchListRequest,
    background_tasks: BackgroundTasks,
    http_request: Request
):
    """
    Optimized search list retrieval with enhanced caching and performance monitoring.
    
    Args:
        request: Search list request containing TUI
        background_tasks: FastAPI background tasks
        http_request: HTTP request object
        
    Returns:
        Cached search results or error response
    """
    start_time = time.time()
    request_data = request.model_dump()
    
    request_metadata = {
        "client_ip": http_request.client.host if http_request.client else "unknown",
        "request_id": http_request.headers.get("x-request-id", f"list_req_{int(time.time() * 1000)}")
    }
    
    try:
        # Get TUI from request
        tui = request_data.get('TUI')
        if not tui:
            raise HTTPException(status_code=400, detail="Missing TUI parameter")
        
        # Try to get cached results
        from app.config import RedisCache
        redis_client = RedisCache.connection()
        
        cached_results = redis_client.get_cache(tui)
        response_time_ms = round((time.time() - start_time) * 1000, 2)
        
        if cached_results:
            # Add retrieval metadata
            cached_results.update({
                "TUI": tui,
                "sh_price": True,
                "retrieved_at": time.time(),
                "response_time_ms": response_time_ms,
                "data_source": "cache_retrieval",
                "cache_hit": True,
                "performance_target_met": response_time_ms <= 3000
            })
            
            # Record successful retrieval
            await performance_analytics.record_metric(
                "search_list_response_time",
                response_time_ms,
                "optimized_search_list",
                {
                    "cache_hit": True,
                    "tui": tui,
                    **request_metadata
                }
            )
            
            headers = {
                "X-Response-Time": str(response_time_ms),
                "X-Cache-Hit": "true",
                "X-Request-ID": request_metadata["request_id"]
            }
            
            return JSONResponse(content=cached_results, headers=headers)
        
        else:
            # No results found
            not_found_response = {
                "TUI": tui,
                "Completed": False,
                "CeilingInfo": None,
                "CurrencyCode": "INR",
                "Notices": ["Search results have expired or not found. Please perform a new search."],
                "Trips": None,
                "Code": "404",
                "Msg": ["Search results not found or expired"],
                "sh_price": False,
                "cache_hit": False,
                "response_time_ms": response_time_ms,
                "data_source": "cache_miss",
                "performance_target_met": response_time_ms <= 3000
            }
            
            # Record cache miss
            await performance_analytics.record_metric(
                "search_list_cache_miss_rate",
                1.0,
                "optimized_search_list",
                {
                    "tui": tui,
                    **request_metadata
                }
            )
            
            headers = {
                "X-Response-Time": str(response_time_ms),
                "X-Cache-Hit": "false",
                "X-Request-ID": request_metadata["request_id"]
            }
            
            return JSONResponse(content=not_found_response, status_code=404, headers=headers)
            
    except Exception as e:
        error_response_time = round((time.time() - start_time) * 1000, 2)
        
        await performance_analytics.record_metric(
            "search_list_error_rate",
            1.0,
            "optimized_search_list",
            {
                "error_type": type(e).__name__,
                "error_message": str(e),
                **request_metadata
            }
        )
        
        error_response = {
            "TUI": request_data.get('TUI'),
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error retrieving search results: {str(e)}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Internal server error occurred"],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": error_response_time,
            "data_source": "error",
            "error_details": str(e),
            "performance_target_met": False
        }
        
        headers = {
            "X-Response-Time": str(error_response_time),
            "X-Error": "true",
            "X-Request-ID": request_metadata["request_id"]
        }
        
        return JSONResponse(content=error_response, status_code=500, headers=headers)


@router.get("/search/performance/stats")
async def get_search_performance_stats():
    """
    Get current search performance statistics.
    
    Returns:
        Performance metrics and statistics
    """
    try:
        # Get performance stats from optimized search service
        search_stats = optimized_flight_search.get_performance_stats()
        
        # Get cache statistics
        from app.microservices.flight_service.cache_service import flight_cache_service
        cache_stats = await flight_cache_service.get_cache_statistics()
        
        # Get analytics dashboard data
        dashboard_data = await performance_analytics.get_performance_dashboard()
        
        return {
            "status": "success",
            "timestamp": time.time(),
            "search_performance": search_stats,
            "cache_performance": cache_stats,
            "system_analytics": dashboard_data,
            "performance_targets": {
                "response_time_target_ms": config.performance_config["response_time_target_ms"],
                "response_time_warning_ms": config.performance_config["response_time_warning_ms"],
                "cache_hit_rate_target": config.performance_config["cache_hit_rate_alert_threshold"]
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve performance stats: {str(e)}")


async def _trigger_performance_optimization(
    request_data: Dict[str, Any],
    response_time_ms: float,
    data_source: str
):
    """
    Background task to trigger performance optimization when response time exceeds target.
    
    Args:
        request_data: Original request data
        response_time_ms: Response time that exceeded target
        data_source: Source of the data (cache, provider, etc.)
    """
    try:
        # Log performance issue
        print(f"Performance optimization triggered: {response_time_ms}ms from {data_source}")
        
        # Trigger cache warming for this route if it was a cache miss
        if data_source in ["provider_async", "provider_sync_fallback"]:
            from app.microservices.flight_service.cache_warming.service import cache_warming_service
            await cache_warming_service.warm_specific_route(request_data)
        
        # Record performance alert
        await performance_analytics.record_metric(
            "performance_optimization_triggered",
            1.0,
            "background_optimization",
            {
                "trigger_response_time_ms": response_time_ms,
                "trigger_data_source": data_source,
                "optimization_type": "cache_warming" if data_source.startswith("provider") else "unknown"
            }
        )
        
    except Exception as e:
        print(f"Performance optimization error: {str(e)}")
