## Benchmark time for `diff-sequences` versus `diff`

A ratio less than 1.0 means `diff-sequences` is faster.

### n = 20

| name   |    % | ratio  | improved  |   rme | baseline  |   rme |
| :----- | ---: | :----- | :-------- | ----: | :-------- | ----: |
| delete |  20% | 0.1824 | 3.0639e-6 | 1.11% | 1.6795e-5 | 0.78% |
| insert |  20% | 0.1367 | 2.5786e-6 | 0.75% | 1.8866e-5 | 0.85% |
| delete |  40% | 0.1015 | 3.0534e-6 | 1.00% | 3.0090e-5 | 0.70% |
| insert |  40% | 0.0791 | 2.6722e-6 | 0.61% | 3.3791e-5 | 0.56% |
| delete |  80% | 0.0410 | 2.8870e-6 | 0.93% | 7.0411e-5 | 0.72% |
| insert |  80% | 0.0371 | 2.5786e-6 | 0.83% | 6.9431e-5 | 0.62% |
| change |  10% | 0.1504 | 2.8703e-6 | 0.71% | 1.9087e-5 | 0.83% |
| change |  20% | 0.1706 | 3.2637e-6 | 0.78% | 1.9127e-5 | 0.62% |
| change |  50% | 0.0944 | 1.2012e-5 | 0.55% | 1.2724e-4 | 0.76% |
| change | 100% | 0.0522 | 1.5422e-5 | 0.61% | 2.9566e-4 | 0.66% |

### n = 200

| name   |    % | ratio  | improved  |   rme | baseline  |   rme |
| :----- | ---: | :----- | :-------- | ----: | :-------- | ----: |
| delete |  20% | 0.1524 | 7.2866e-5 | 0.75% | 4.7797e-4 | 0.80% |
| insert |  20% | 0.1226 | 6.1561e-5 | 0.58% | 5.0198e-4 | 0.66% |
| delete |  40% | 0.1118 | 1.5674e-4 | 0.67% | 1.4020e-3 | 0.58% |
| insert |  40% | 0.0894 | 1.2906e-4 | 0.64% | 1.4435e-3 | 0.53% |
| delete |  80% | 0.0796 | 3.0119e-4 | 0.58% | 3.7852e-3 | 0.52% |
| insert |  80% | 0.0734 | 2.4713e-4 | 0.67% | 3.3653e-3 | 0.54% |
| change |  10% | 0.1572 | 7.2965e-5 | 0.48% | 4.6426e-4 | 0.73% |
| change |  20% | 0.1446 | 7.0056e-5 | 0.69% | 4.8456e-4 | 0.53% |
| change |  50% | 0.0764 | 6.5638e-4 | 0.67% | 8.5946e-3 | 0.70% |
| change | 100% | 0.0525 | 1.1160e-3 | 0.51% | 2.1249e-2 | 0.63% |

### n = 2000

| name   |    % | ratio  | improved  |   rme | baseline  |   rme |
| :----- | ---: | :----- | :-------- | ----: | :-------- | ----: |
| delete |  20% | 0.0669 | 3.4073e-3 | 0.54% | 5.0922e-2 | 0.54% |
| insert |  20% | 0.0588 | 2.8273e-3 | 0.51% | 4.8111e-2 | 0.46% |
| delete |  40% | 0.0517 | 1.1048e-2 | 0.52% | 2.1367e-1 | 0.47% |
| insert |  40% | 0.0460 | 9.1469e-3 | 0.37% | 1.9878e-1 | 0.26% |
| delete |  80% | 0.0563 | 2.7426e-2 | 0.56% | 4.8674e-1 | 0.36% |
| insert |  80% | 0.0506 | 2.2208e-2 | 0.35% | 4.3888e-1 | 0.47% |
| change |  10% | 0.0716 | 3.1267e-3 | 1.21% | 4.3652e-2 | 0.56% |
| change |  20% | 0.0621 | 3.0197e-3 | 0.72% | 4.8652e-2 | 0.45% |
| change |  50% | 0.0083 | 5.4250e-2 | 0.62% | 6.5595e+0 | 3.60% |
| change | 100% | 0.0493 | 1.0534e-1 | 0.71% | 2.1362e+0 | 0.21% |
