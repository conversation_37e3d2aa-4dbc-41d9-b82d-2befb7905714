import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock the WebSocket service to avoid connection errors in tests
jest.mock('./services/websocket', () => ({
  webSocketService: {
    on: jest.fn(),
    subscribeToAll: jest.fn(),
    getConnectionStatus: () => false,
  },
}));

// Mock react-router-dom to avoid routing issues in tests
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  BrowserRouter: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Routes: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Route: ({ element }: { element: React.ReactNode }) => <div>{element}</div>,
  Outlet: () => <div>Dashboard Content</div>,
  useLocation: () => ({ pathname: '/' }),
  useNavigate: () => jest.fn(),
}));

test('renders without crashing', () => {
  render(<App />);
  // Just test that the app renders without throwing errors
  expect(document.body).toBeInTheDocument();
});

test('app structure is correct', () => {
  const { container } = render(<App />);
  // Check that the app container exists
  expect(container.firstChild).toBeInTheDocument();
});
