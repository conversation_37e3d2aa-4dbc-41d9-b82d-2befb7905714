from dotenv import load_dotenv
import os
import redis, json, hashlib
import mysql.connector
from celery import Celery

load_dotenv()
env = os.environ

REDIS_SERVER_DB=env.get("REDIS_SERVER_DB")
REDIS_SERVER_HOST=env.get("REDIS_SERVER_HOST")
REDIS_SERVER_PORT=env.get("REDIS_SERVER_PORT")

def get_database_connection():
    return mysql.connector.connect(
        host=env.get('DATABASE_HOST'),
        user=env.get('DATABASE_USER'),
        password=env.get('DATABASE_PASSWORD'),
        database=env.get('DATABASE_NAME')
    )

class RedisCache:
	__instance = None

	@staticmethod 
	def connection():
		current_instance = RedisCache.__instance
		if current_instance is None or current_instance.connection is None:
			RedisCache()
		return RedisCache.__instance

	def __init__(self):
		current_instance = RedisCache.__instance
		if not(current_instance is None or current_instance.connection is None):
			raise Exception("This class is a singleton!")
		else:
			try:
				cache_settings = {
                                    'db': REDIS_SERVER_DB,
                                    'host': REDIS_SERVER_HOST,
                                    'port': REDIS_SERVER_PORT 
                                }
			except AttributeError as error:
				cache_settings = None
			
			if cache_settings:
				self.connection = redis.Redis( host=cache_settings.get('host', 'localhost'),
									port=cache_settings.get('port', 6379),
									db=cache_settings.get('db', 1))

		RedisCache.__instance = self

	@staticmethod
	def generate_cache_key(payload, remove_keys=[]):
		payload_dup = payload.copy()
		for e in remove_keys: 
			payload_dup.pop(e)
		hash_string = str(json.dumps(payload_dup, sort_keys=True))
		return hashlib.sha256(hash_string.encode()).hexdigest()

	def get_cache(self, key, content_type='json'):
		response = self.connection.get(key)
		if response:
			if content_type == 'json':
				return json.loads(response)
			else: 
				return response

	def set_cache(self, key, response,  expiry_seconds=86400, content_type='json'):
		if content_type == 'json':
			response = json.dumps(response)
		self.connection.set(f"{key}", response, expiry_seconds)

	def remove_cache(self,key):
		self.connection.delete(key)	

	def get_keys_by_pattern(self, pattern):
		return self.connection.keys(f"{pattern}*")
	
# Celery configuration
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://172.22.116.62:6379/0")
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", "redis://172.22.116.62:6379/0")

celery_app = Celery("fast_travel_backend",  broker=CELERY_BROKER_URL, backend=CELERY_RESULT_BACKEND)
# celery_app.config_from_object("app.config")
celery_app.conf.update(
    task_routes={"tasks.*": {"queue": "default"}},
	broker_connection_retry_on_startup=True  # New setting for Celery 6.0+ compatibility
)	
celery_app.autodiscover_tasks(["app.microservices.flight_service.search_service.tasks", "app.microservices.flight_service.detail_service.tasks"])