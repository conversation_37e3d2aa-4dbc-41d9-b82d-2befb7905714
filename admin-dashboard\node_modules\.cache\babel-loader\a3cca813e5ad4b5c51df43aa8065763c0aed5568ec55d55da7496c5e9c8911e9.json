{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PROJECTS\\\\fast_travel_backend\\\\admin-dashboard\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Divider, Typography, Box, Chip, useTheme, alpha } from '@mui/material';\nimport { Dashboard as DashboardIcon, Speed as PerformanceIcon, Storage as CacheIcon, Search as SearchIcon, Settings as SettingsIcon, Assessment as AnalyticsIcon, BugReport as ErrorIcon, CloudQueue as TripJackIcon, People as UsersIcon, Notifications as AlertsIcon, GetApp as ExportIcon, HealthAndSafety as HealthIcon } from '@mui/icons-material';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAppContext } from '../../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DRAWER_WIDTH = 280;\nconst Sidebar = () => {\n  _s();\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    state,\n    setSidebarOpen\n  } = useAppContext();\n  const menuSections = [{\n    title: 'Overview',\n    items: [{\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this),\n      path: '/'\n    }, {\n      id: 'health',\n      label: 'System Health',\n      icon: /*#__PURE__*/_jsxDEV(HealthIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this),\n      path: '/health',\n      color: state.systemHealth.api === 'healthy' ? 'success' : 'error'\n    }]\n  }, {\n    title: 'Monitoring',\n    items: [{\n      id: 'performance',\n      label: 'Performance',\n      icon: /*#__PURE__*/_jsxDEV(PerformanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this),\n      path: '/performance'\n    }, {\n      id: 'cache',\n      label: 'Cache Management',\n      icon: /*#__PURE__*/_jsxDEV(CacheIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 17\n      }, this),\n      path: '/cache'\n    }, {\n      id: 'searches',\n      label: 'Flight Searches',\n      icon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this),\n      path: '/searches',\n      badge: state.activeSearches.length\n    }, {\n      id: 'tripjack',\n      label: 'TripJack Integration',\n      icon: /*#__PURE__*/_jsxDEV(TripJackIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this),\n      path: '/tripjack',\n      color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning'\n    }]\n  }, {\n    title: 'Analytics',\n    items: [{\n      id: 'analytics',\n      label: 'Route Analytics',\n      icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 17\n      }, this),\n      path: '/analytics'\n    }, {\n      id: 'errors',\n      label: 'Error Logs',\n      icon: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this),\n      path: '/errors'\n    }, {\n      id: 'alerts',\n      label: 'System Alerts',\n      icon: /*#__PURE__*/_jsxDEV(AlertsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 17\n      }, this),\n      path: '/alerts',\n      badge: state.alerts.filter(alert => !alert.acknowledged).length,\n      color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning'\n    }]\n  }, {\n    title: 'Administration',\n    items: [{\n      id: 'settings',\n      label: 'Configuration',\n      icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 17\n      }, this),\n      path: '/settings'\n    }, {\n      id: 'users',\n      label: 'User Management',\n      icon: /*#__PURE__*/_jsxDEV(UsersIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 17\n      }, this),\n      path: '/users'\n    }, {\n      id: 'export',\n      label: 'Data Export',\n      icon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 17\n      }, this),\n      path: '/export'\n    }]\n  }];\n  const handleItemClick = path => {\n    navigate(path);\n    if (window.innerWidth < theme.breakpoints.values.md) {\n      setSidebarOpen(false);\n    }\n  };\n  const isSelected = path => {\n    if (path === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(path);\n  };\n  const renderMenuItem = item => /*#__PURE__*/_jsxDEV(ListItem, {\n    disablePadding: true,\n    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isSelected(item.path),\n      onClick: () => handleItemClick(item.path),\n      sx: {\n        borderRadius: 1,\n        mx: 1,\n        mb: 0.5,\n        '&.Mui-selected': {\n          backgroundColor: alpha(theme.palette.primary.main, 0.12),\n          '&:hover': {\n            backgroundColor: alpha(theme.palette.primary.main, 0.16)\n          }\n        },\n        '&:hover': {\n          backgroundColor: alpha(theme.palette.action.hover, 0.08)\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        sx: {\n          color: isSelected(item.path) ? theme.palette.primary.main : theme.palette.text.secondary,\n          minWidth: 40\n        },\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: item.label,\n        sx: {\n          '& .MuiListItemText-primary': {\n            fontSize: '0.875rem',\n            fontWeight: isSelected(item.path) ? 600 : 400,\n            color: isSelected(item.path) ? theme.palette.primary.main : theme.palette.text.primary\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), item.badge !== undefined && item.badge > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n        label: item.badge,\n        size: \"small\",\n        color: item.color || 'primary',\n        sx: {\n          height: 20,\n          fontSize: '0.75rem',\n          fontWeight: 600\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this), item.color && item.badge === undefined && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          backgroundColor: getColorFromPalette(item.color)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)\n  }, item.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n  const drawerContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: `1px solid ${theme.palette.divider}`,\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: 700,\n        children: \"Fast Travel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          opacity: 0.9\n        },\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: `1px solid ${theme.palette.divider}`\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 8,\n            height: 8,\n            borderRadius: '50%',\n            backgroundColor: state.wsConnected ? theme.palette.success.main : theme.palette.error.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: state.wsConnected ? 'Real-time Connected' : 'Offline Mode'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflow: 'auto',\n        py: 1\n      },\n      children: menuSections.map((section, index) => /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"overline\",\n          sx: {\n            px: 2,\n            py: 1,\n            display: 'block',\n            color: theme.palette.text.secondary,\n            fontSize: '0.75rem',\n            fontWeight: 600,\n            letterSpacing: 1\n          },\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          sx: {\n            px: 0\n          },\n          children: section.items.map(renderMenuItem)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), index < menuSections.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mx: 2,\n            my: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 15\n        }, this)]\n      }, section.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderTop: `1px solid ${theme.palette.divider}`,\n        backgroundColor: alpha(theme.palette.background.paper, 0.8)\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        display: \"block\",\n        children: \"Version 1.0.0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        display: \"block\",\n        children: [\"Last updated: \", new Date().toLocaleDateString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"persistent\",\n      anchor: \"left\",\n      open: state.sidebarOpen,\n      sx: {\n        display: {\n          xs: 'none',\n          md: 'block'\n        },\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box',\n          borderRight: `1px solid ${theme.palette.divider}`,\n          backgroundColor: theme.palette.background.paper\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"temporary\",\n      anchor: \"left\",\n      open: state.sidebarOpen,\n      onClose: () => setSidebarOpen(false),\n      ModalProps: {\n        keepMounted: true // Better open performance on mobile\n      },\n      sx: {\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box'\n        }\n      },\n      children: drawerContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"eo3I+AzjG+Wp98ovqyCajcgBx50=\", false, function () {\n  return [useTheme, useLocation, useNavigate, useAppContext];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Drawer", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "Divider", "Typography", "Box", "Chip", "useTheme", "alpha", "Dashboard", "DashboardIcon", "Speed", "PerformanceIcon", "Storage", "CacheIcon", "Search", "SearchIcon", "Settings", "SettingsIcon", "Assessment", "AnalyticsIcon", "BugReport", "ErrorIcon", "CloudQueue", "TripJackIcon", "People", "UsersIcon", "Notifications", "AlertsIcon", "GetApp", "ExportIcon", "HealthAndSafety", "HealthIcon", "useLocation", "useNavigate", "useAppContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DRAWER_WIDTH", "Sidebar", "_s", "theme", "location", "navigate", "state", "setSidebarOpen", "menuSections", "title", "items", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "color", "systemHealth", "api", "badge", "activeSearches", "length", "tripjack", "alerts", "filter", "alert", "acknowledged", "some", "severity", "handleItemClick", "window", "innerWidth", "breakpoints", "values", "md", "isSelected", "pathname", "startsWith", "renderMenuItem", "item", "disablePadding", "children", "selected", "onClick", "sx", "borderRadius", "mx", "mb", "backgroundColor", "palette", "primary", "main", "action", "hover", "text", "secondary", "min<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "undefined", "size", "height", "width", "getColorFromPalette", "drawerContent", "display", "flexDirection", "p", "borderBottom", "divider", "background", "dark", "variant", "opacity", "alignItems", "gap", "wsConnected", "success", "error", "flex", "overflow", "py", "map", "section", "index", "px", "letterSpacing", "dense", "my", "borderTop", "paper", "Date", "toLocaleDateString", "anchor", "open", "sidebarOpen", "xs", "boxSizing", "borderRight", "onClose", "ModalProps", "keepMounted", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PROJECTS/fast_travel_backend/admin-dashboard/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Drawer,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Typography,\n  Box,\n  Chip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  Speed as PerformanceIcon,\n  Storage as CacheIcon,\n  Search as SearchIcon,\n  Settings as SettingsIcon,\n  Assessment as AnalyticsIcon,\n  BugReport as ErrorIcon,\n  CloudQueue as TripJackIcon,\n  People as UsersIcon,\n  Notifications as AlertsIcon,\n  GetApp as ExportIcon,\n  HealthAndSafety as HealthIcon,\n} from '@mui/icons-material';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAppContext } from '../../contexts/AppContext';\n\nconst DRAWER_WIDTH = 280;\n\ninterface MenuItem {\n  id: string;\n  label: string;\n  icon: React.ReactElement;\n  path: string;\n  badge?: number;\n  color?: string;\n}\n\ninterface MenuSection {\n  title: string;\n  items: MenuItem[];\n}\n\nconst Sidebar: React.FC = () => {\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { state, setSidebarOpen } = useAppContext();\n\n  const menuSections: MenuSection[] = [\n    {\n      title: 'Overview',\n      items: [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: <DashboardIcon />,\n          path: '/',\n        },\n        {\n          id: 'health',\n          label: 'System Health',\n          icon: <HealthIcon />,\n          path: '/health',\n          color: state.systemHealth.api === 'healthy' ? 'success' : 'error',\n        },\n      ],\n    },\n    {\n      title: 'Monitoring',\n      items: [\n        {\n          id: 'performance',\n          label: 'Performance',\n          icon: <PerformanceIcon />,\n          path: '/performance',\n        },\n        {\n          id: 'cache',\n          label: 'Cache Management',\n          icon: <CacheIcon />,\n          path: '/cache',\n        },\n        {\n          id: 'searches',\n          label: 'Flight Searches',\n          icon: <SearchIcon />,\n          path: '/searches',\n          badge: state.activeSearches.length,\n        },\n        {\n          id: 'tripjack',\n          label: 'TripJack Integration',\n          icon: <TripJackIcon />,\n          path: '/tripjack',\n          color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning',\n        },\n      ],\n    },\n    {\n      title: 'Analytics',\n      items: [\n        {\n          id: 'analytics',\n          label: 'Route Analytics',\n          icon: <AnalyticsIcon />,\n          path: '/analytics',\n        },\n        {\n          id: 'errors',\n          label: 'Error Logs',\n          icon: <ErrorIcon />,\n          path: '/errors',\n        },\n        {\n          id: 'alerts',\n          label: 'System Alerts',\n          icon: <AlertsIcon />,\n          path: '/alerts',\n          badge: state.alerts.filter(alert => !alert.acknowledged).length,\n          color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning',\n        },\n      ],\n    },\n    {\n      title: 'Administration',\n      items: [\n        {\n          id: 'settings',\n          label: 'Configuration',\n          icon: <SettingsIcon />,\n          path: '/settings',\n        },\n        {\n          id: 'users',\n          label: 'User Management',\n          icon: <UsersIcon />,\n          path: '/users',\n        },\n        {\n          id: 'export',\n          label: 'Data Export',\n          icon: <ExportIcon />,\n          path: '/export',\n        },\n      ],\n    },\n  ];\n\n  const handleItemClick = (path: string) => {\n    navigate(path);\n    if (window.innerWidth < theme.breakpoints.values.md) {\n      setSidebarOpen(false);\n    }\n  };\n\n  const isSelected = (path: string) => {\n    if (path === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(path);\n  };\n\n  const renderMenuItem = (item: MenuItem) => (\n    <ListItem key={item.id} disablePadding>\n      <ListItemButton\n        selected={isSelected(item.path)}\n        onClick={() => handleItemClick(item.path)}\n        sx={{\n          borderRadius: 1,\n          mx: 1,\n          mb: 0.5,\n          '&.Mui-selected': {\n            backgroundColor: alpha(theme.palette.primary.main, 0.12),\n            '&:hover': {\n              backgroundColor: alpha(theme.palette.primary.main, 0.16),\n            },\n          },\n          '&:hover': {\n            backgroundColor: alpha(theme.palette.action.hover, 0.08),\n          },\n        }}\n      >\n        <ListItemIcon\n          sx={{\n            color: isSelected(item.path)\n              ? theme.palette.primary.main\n              : theme.palette.text.secondary,\n            minWidth: 40,\n          }}\n        >\n          {item.icon}\n        </ListItemIcon>\n        <ListItemText\n          primary={item.label}\n          sx={{\n            '& .MuiListItemText-primary': {\n              fontSize: '0.875rem',\n              fontWeight: isSelected(item.path) ? 600 : 400,\n              color: isSelected(item.path)\n                ? theme.palette.primary.main\n                : theme.palette.text.primary,\n            },\n          }}\n        />\n        {item.badge !== undefined && item.badge > 0 && (\n          <Chip\n            label={item.badge}\n            size=\"small\"\n            color={item.color as any || 'primary'}\n            sx={{\n              height: 20,\n              fontSize: '0.75rem',\n              fontWeight: 600,\n            }}\n          />\n        )}\n        {item.color && item.badge === undefined && (\n          <Box\n            sx={{\n              width: 8,\n              height: 8,\n              borderRadius: '50%',\n              backgroundColor: getColorFromPalette(item.color),\n            }}\n          />\n        )}\n      </ListItemButton>\n    </ListItem>\n  );\n\n  const drawerContent = (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Box\n        sx={{\n          p: 2,\n          borderBottom: `1px solid ${theme.palette.divider}`,\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n          color: 'white',\n        }}\n      >\n        <Typography variant=\"h6\" fontWeight={700}>\n          Fast Travel\n        </Typography>\n        <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n          Admin Dashboard\n        </Typography>\n      </Box>\n\n      {/* Connection Status */}\n      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <Box\n            sx={{\n              width: 8,\n              height: 8,\n              borderRadius: '50%',\n              backgroundColor: state.wsConnected\n                ? theme.palette.success.main\n                : theme.palette.error.main,\n            }}\n          />\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {state.wsConnected ? 'Real-time Connected' : 'Offline Mode'}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Menu Sections */}\n      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>\n        {menuSections.map((section, index) => (\n          <Box key={section.title}>\n            <Typography\n              variant=\"overline\"\n              sx={{\n                px: 2,\n                py: 1,\n                display: 'block',\n                color: theme.palette.text.secondary,\n                fontSize: '0.75rem',\n                fontWeight: 600,\n                letterSpacing: 1,\n              }}\n            >\n              {section.title}\n            </Typography>\n            <List dense sx={{ px: 0 }}>\n              {section.items.map(renderMenuItem)}\n            </List>\n            {index < menuSections.length - 1 && (\n              <Divider sx={{ mx: 2, my: 1 }} />\n            )}\n          </Box>\n        ))}\n      </Box>\n\n      {/* Footer */}\n      <Box\n        sx={{\n          p: 2,\n          borderTop: `1px solid ${theme.palette.divider}`,\n          backgroundColor: alpha(theme.palette.background.paper, 0.8),\n        }}\n      >\n        <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n          Version 1.0.0\n        </Typography>\n        <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n          Last updated: {new Date().toLocaleDateString()}\n        </Typography>\n      </Box>\n    </Box>\n  );\n\n  return (\n    <>\n      {/* Desktop Drawer */}\n      <Drawer\n        variant=\"persistent\"\n        anchor=\"left\"\n        open={state.sidebarOpen}\n        sx={{\n          display: { xs: 'none', md: 'block' },\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n            borderRight: `1px solid ${theme.palette.divider}`,\n            backgroundColor: theme.palette.background.paper,\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n\n      {/* Mobile Drawer */}\n      <Drawer\n        variant=\"temporary\"\n        anchor=\"left\"\n        open={state.sidebarOpen}\n        onClose={() => setSidebarOpen(false)}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile\n        }}\n        sx={{\n          display: { xs: 'block', md: 'none' },\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,eAAe,EACxBC,OAAO,IAAIC,SAAS,EACpBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,aAAa,EAC3BC,SAAS,IAAIC,SAAS,EACtBC,UAAU,IAAIC,YAAY,EAC1BC,MAAM,IAAIC,SAAS,EACnBC,aAAa,IAAIC,UAAU,EAC3BC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,UAAU,QACxB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,YAAY,GAAG,GAAG;AAgBxB,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,KAAK,GAAGpC,QAAQ,CAAC,CAAC;EACxB,MAAMqC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,KAAK;IAAEC;EAAe,CAAC,GAAGZ,aAAa,CAAC,CAAC;EAEjD,MAAMa,YAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,WAAW;MAClBC,IAAI,eAAEhB,OAAA,CAAC3B,aAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAEhB,OAAA,CAACL,UAAU;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAEb,KAAK,CAACc,YAAY,CAACC,GAAG,KAAK,SAAS,GAAG,SAAS,GAAG;IAC5D,CAAC;EAEL,CAAC,EACD;IACEZ,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,aAAa;MACjBC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAEhB,OAAA,CAACzB,eAAe;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,eAAEhB,OAAA,CAACvB,SAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAEhB,OAAA,CAACrB,UAAU;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,WAAW;MACjBI,KAAK,EAAEhB,KAAK,CAACiB,cAAc,CAACC;IAC9B,CAAC,EACD;MACEb,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,eAAEhB,OAAA,CAACb,YAAY;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAEb,KAAK,CAACc,YAAY,CAACK,QAAQ,KAAK,WAAW,GAAG,SAAS,GAAG;IACnE,CAAC;EAEL,CAAC,EACD;IACEhB,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAEhB,OAAA,CAACjB,aAAa;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,YAAY;MACnBC,IAAI,eAAEhB,OAAA,CAACf,SAAS;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAEhB,OAAA,CAACT,UAAU;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,SAAS;MACfI,KAAK,EAAEhB,KAAK,CAACoB,MAAM,CAACC,MAAM,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,YAAY,CAAC,CAACL,MAAM;MAC/DL,KAAK,EAAEb,KAAK,CAACoB,MAAM,CAACI,IAAI,CAACF,KAAK,IAAIA,KAAK,CAACG,QAAQ,KAAK,UAAU,CAAC,GAAG,OAAO,GAAG;IAC/E,CAAC;EAEL,CAAC,EACD;IACEtB,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAEhB,OAAA,CAACnB,YAAY;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,eAAEhB,OAAA,CAACX,SAAS;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAEhB,OAAA,CAACP,UAAU;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,CACF;EAED,MAAMc,eAAe,GAAId,IAAY,IAAK;IACxCb,QAAQ,CAACa,IAAI,CAAC;IACd,IAAIe,MAAM,CAACC,UAAU,GAAG/B,KAAK,CAACgC,WAAW,CAACC,MAAM,CAACC,EAAE,EAAE;MACnD9B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM+B,UAAU,GAAIpB,IAAY,IAAK;IACnC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,OAAOd,QAAQ,CAACmC,QAAQ,KAAK,GAAG;IAClC;IACA,OAAOnC,QAAQ,CAACmC,QAAQ,CAACC,UAAU,CAACtB,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMuB,cAAc,GAAIC,IAAc,iBACpC7C,OAAA,CAACtC,QAAQ;IAAeoF,cAAc;IAAAC,QAAA,eACpC/C,OAAA,CAACrC,cAAc;MACbqF,QAAQ,EAAEP,UAAU,CAACI,IAAI,CAACxB,IAAI,CAAE;MAChC4B,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAACU,IAAI,CAACxB,IAAI,CAAE;MAC1C6B,EAAE,EAAE;QACFC,YAAY,EAAE,CAAC;QACfC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,GAAG;QACP,gBAAgB,EAAE;UAChBC,eAAe,EAAEnF,KAAK,CAACmC,KAAK,CAACiD,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC;UACxD,SAAS,EAAE;YACTH,eAAe,EAAEnF,KAAK,CAACmC,KAAK,CAACiD,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI;UACzD;QACF,CAAC;QACD,SAAS,EAAE;UACTH,eAAe,EAAEnF,KAAK,CAACmC,KAAK,CAACiD,OAAO,CAACG,MAAM,CAACC,KAAK,EAAE,IAAI;QACzD;MACF,CAAE;MAAAZ,QAAA,gBAEF/C,OAAA,CAACpC,YAAY;QACXsF,EAAE,EAAE;UACF5B,KAAK,EAAEmB,UAAU,CAACI,IAAI,CAACxB,IAAI,CAAC,GACxBf,KAAK,CAACiD,OAAO,CAACC,OAAO,CAACC,IAAI,GAC1BnD,KAAK,CAACiD,OAAO,CAACK,IAAI,CAACC,SAAS;UAChCC,QAAQ,EAAE;QACZ,CAAE;QAAAf,QAAA,EAEDF,IAAI,CAAC7B;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACfpB,OAAA,CAACnC,YAAY;QACX2F,OAAO,EAAEX,IAAI,CAAC9B,KAAM;QACpBmC,EAAE,EAAE;UACF,4BAA4B,EAAE;YAC5Ba,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAEvB,UAAU,CAACI,IAAI,CAACxB,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;YAC7CC,KAAK,EAAEmB,UAAU,CAACI,IAAI,CAACxB,IAAI,CAAC,GACxBf,KAAK,CAACiD,OAAO,CAACC,OAAO,CAACC,IAAI,GAC1BnD,KAAK,CAACiD,OAAO,CAACK,IAAI,CAACJ;UACzB;QACF;MAAE;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDyB,IAAI,CAACpB,KAAK,KAAKwC,SAAS,IAAIpB,IAAI,CAACpB,KAAK,GAAG,CAAC,iBACzCzB,OAAA,CAAC/B,IAAI;QACH8C,KAAK,EAAE8B,IAAI,CAACpB,KAAM;QAClByC,IAAI,EAAC,OAAO;QACZ5C,KAAK,EAAEuB,IAAI,CAACvB,KAAK,IAAW,SAAU;QACtC4B,EAAE,EAAE;UACFiB,MAAM,EAAE,EAAE;UACVJ,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd;MAAE;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EACAyB,IAAI,CAACvB,KAAK,IAAIuB,IAAI,CAACpB,KAAK,KAAKwC,SAAS,iBACrCjE,OAAA,CAAChC,GAAG;QACFkF,EAAE,EAAE;UACFkB,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE,CAAC;UACThB,YAAY,EAAE,KAAK;UACnBG,eAAe,EAAEe,mBAAmB,CAACxB,IAAI,CAACvB,KAAK;QACjD;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC,GA/DJyB,IAAI,CAAC/B,EAAE;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAgEZ,CACX;EAED,MAAMkD,aAAa,gBACjBtE,OAAA,CAAChC,GAAG;IAACkF,EAAE,EAAE;MAAEiB,MAAM,EAAE,MAAM;MAAEI,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAzB,QAAA,gBAEpE/C,OAAA,CAAChC,GAAG;MACFkF,EAAE,EAAE;QACFuB,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,aAAapE,KAAK,CAACiD,OAAO,CAACoB,OAAO,EAAE;QAClDC,UAAU,EAAE,2BAA2BtE,KAAK,CAACiD,OAAO,CAACC,OAAO,CAACC,IAAI,QAAQnD,KAAK,CAACiD,OAAO,CAACC,OAAO,CAACqB,IAAI,QAAQ;QAC3GvD,KAAK,EAAE;MACT,CAAE;MAAAyB,QAAA,gBAEF/C,OAAA,CAACjC,UAAU;QAAC+G,OAAO,EAAC,IAAI;QAACd,UAAU,EAAE,GAAI;QAAAjB,QAAA,EAAC;MAE1C;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpB,OAAA,CAACjC,UAAU;QAAC+G,OAAO,EAAC,OAAO;QAAC5B,EAAE,EAAE;UAAE6B,OAAO,EAAE;QAAI,CAAE;QAAAhC,QAAA,EAAC;MAElD;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNpB,OAAA,CAAChC,GAAG;MAACkF,EAAE,EAAE;QAAEuB,CAAC,EAAE,CAAC;QAAEC,YAAY,EAAE,aAAapE,KAAK,CAACiD,OAAO,CAACoB,OAAO;MAAG,CAAE;MAAA5B,QAAA,eACpE/C,OAAA,CAAChC,GAAG;QAACkF,EAAE,EAAE;UAAEqB,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAlC,QAAA,gBACzD/C,OAAA,CAAChC,GAAG;UACFkF,EAAE,EAAE;YACFkB,KAAK,EAAE,CAAC;YACRD,MAAM,EAAE,CAAC;YACThB,YAAY,EAAE,KAAK;YACnBG,eAAe,EAAE7C,KAAK,CAACyE,WAAW,GAC9B5E,KAAK,CAACiD,OAAO,CAAC4B,OAAO,CAAC1B,IAAI,GAC1BnD,KAAK,CAACiD,OAAO,CAAC6B,KAAK,CAAC3B;UAC1B;QAAE;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFpB,OAAA,CAACjC,UAAU;UAAC+G,OAAO,EAAC,SAAS;UAACxD,KAAK,EAAC,gBAAgB;UAAAyB,QAAA,EACjDtC,KAAK,CAACyE,WAAW,GAAG,qBAAqB,GAAG;QAAc;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA,CAAChC,GAAG;MAACkF,EAAE,EAAE;QAAEmC,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxC,QAAA,EAC3CpC,YAAY,CAAC6E,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC/B1F,OAAA,CAAChC,GAAG;QAAA+E,QAAA,gBACF/C,OAAA,CAACjC,UAAU;UACT+G,OAAO,EAAC,UAAU;UAClB5B,EAAE,EAAE;YACFyC,EAAE,EAAE,CAAC;YACLJ,EAAE,EAAE,CAAC;YACLhB,OAAO,EAAE,OAAO;YAChBjD,KAAK,EAAEhB,KAAK,CAACiD,OAAO,CAACK,IAAI,CAACC,SAAS;YACnCE,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,GAAG;YACf4B,aAAa,EAAE;UACjB,CAAE;UAAA7C,QAAA,EAED0C,OAAO,CAAC7E;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbpB,OAAA,CAACvC,IAAI;UAACoI,KAAK;UAAC3C,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAA5C,QAAA,EACvB0C,OAAO,CAAC5E,KAAK,CAAC2E,GAAG,CAAC5C,cAAc;QAAC;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACNsE,KAAK,GAAG/E,YAAY,CAACgB,MAAM,GAAG,CAAC,iBAC9B3B,OAAA,CAAClC,OAAO;UAACoF,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAE0C,EAAE,EAAE;UAAE;QAAE;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjC;MAAA,GApBOqE,OAAO,CAAC7E,KAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBlB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpB,OAAA,CAAChC,GAAG;MACFkF,EAAE,EAAE;QACFuB,CAAC,EAAE,CAAC;QACJsB,SAAS,EAAE,aAAazF,KAAK,CAACiD,OAAO,CAACoB,OAAO,EAAE;QAC/CrB,eAAe,EAAEnF,KAAK,CAACmC,KAAK,CAACiD,OAAO,CAACqB,UAAU,CAACoB,KAAK,EAAE,GAAG;MAC5D,CAAE;MAAAjD,QAAA,gBAEF/C,OAAA,CAACjC,UAAU;QAAC+G,OAAO,EAAC,SAAS;QAACxD,KAAK,EAAC,gBAAgB;QAACiD,OAAO,EAAC,OAAO;QAAAxB,QAAA,EAAC;MAErE;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpB,OAAA,CAACjC,UAAU;QAAC+G,OAAO,EAAC,SAAS;QAACxD,KAAK,EAAC,gBAAgB;QAACiD,OAAO,EAAC,OAAO;QAAAxB,QAAA,GAAC,gBACrD,EAAC,IAAIkD,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAAA;QAAAjF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEpB,OAAA,CAAAE,SAAA;IAAA6C,QAAA,gBAEE/C,OAAA,CAACxC,MAAM;MACLsH,OAAO,EAAC,YAAY;MACpBqB,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE3F,KAAK,CAAC4F,WAAY;MACxBnD,EAAE,EAAE;QACFqB,OAAO,EAAE;UAAE+B,EAAE,EAAE,MAAM;UAAE9D,EAAE,EAAE;QAAQ,CAAC;QACpC,oBAAoB,EAAE;UACpB4B,KAAK,EAAEjE,YAAY;UACnBoG,SAAS,EAAE,YAAY;UACvBC,WAAW,EAAE,aAAalG,KAAK,CAACiD,OAAO,CAACoB,OAAO,EAAE;UACjDrB,eAAe,EAAEhD,KAAK,CAACiD,OAAO,CAACqB,UAAU,CAACoB;QAC5C;MACF,CAAE;MAAAjD,QAAA,EAEDuB;IAAa;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGTpB,OAAA,CAACxC,MAAM;MACLsH,OAAO,EAAC,WAAW;MACnBqB,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE3F,KAAK,CAAC4F,WAAY;MACxBI,OAAO,EAAEA,CAAA,KAAM/F,cAAc,CAAC,KAAK,CAAE;MACrCgG,UAAU,EAAE;QACVC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAE;MACFzD,EAAE,EAAE;QACFqB,OAAO,EAAE;UAAE+B,EAAE,EAAE,OAAO;UAAE9D,EAAE,EAAE;QAAO,CAAC;QACpC,oBAAoB,EAAE;UACpB4B,KAAK,EAAEjE,YAAY;UACnBoG,SAAS,EAAE;QACb;MACF,CAAE;MAAAxD,QAAA,EAEDuB;IAAa;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAACf,EAAA,CAzTID,OAAiB;EAAA,QACPlC,QAAQ,EACL0B,WAAW,EACXC,WAAW,EACMC,aAAa;AAAA;AAAA8G,EAAA,GAJ3CxG,OAAiB;AA2TvB,eAAeA,OAAO;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}