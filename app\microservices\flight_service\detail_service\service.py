"""
The core business logic for the flight detail service is implemented here.
This file processes incoming detail requests, interacts with providers,
and utilizes caching to compile results.
It’s crucial for implementing the main functionality of our service.
"""

import time
from datetime import datetime, timezone

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service, generate_detail_cache_key
from app.microservices.flight_service.detail_service.providers.tripjack.request_creator import (
    review_translate_client_to_provider,
)
from app.microservices.flight_service.detail_service.providers.tripjack.response_converter import (
    review_translate_provider_to_client,
)
from app.microservices.flight_service.detail_service.tasks import fetch_flight_detail_task
from app.microservices.flight_service.detail_service.utils import get_detail_ids
from app.microservices.flight_service.utils.provider_utils import make_provider_api_call
from app.microservices.flight_service import config

class FlightDetail:
    def __init__(self):
        self.cache_service = flight_cache_service
        self.redis_client = RedisCache.connection()

    def detail(self, request_data):
        """
        Enhanced flight details with caching enabled and improved error handling.

        Args:
            request_data (dict): The request data containing fare information.

        Returns:
            dict: The response with flight details or cached results.
        """
        start_time = time.time()

        try:
            # Extract fare ID from the request data using a helper function
            fare_id = get_detail_ids(request_data)

            # Generate cache key for detail request
            cache_key = generate_detail_cache_key(fare_id, request_data)

            # Generate a unique identifier for the request (TUI - Transaction Unique Identifier)
            tui = f"{fare_id}|TJ|detail"

            # Prepare context for the provider request with fare ID and TUI
            context = {"fare_id": fare_id, "TUI": tui}

            # Step 1: Check if the results are already cached in Redis
            cached_results = self.redis_client.get_cache(cache_key)
            if cached_results:
                # Cache hit - trigger background refresh if data is getting stale
                self._trigger_background_refresh_if_needed(request_data, cached_results, context, fare_id)

                # Add performance metadata
                cached_results.update({
                    "TUI": tui,
                    "cache_hit": True,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "cache"
                })

                return cached_results

            # Step 2: Cache miss - fetch from provider
            return self._fetch_from_provider(request_data, context, cache_key, start_time)

        except Exception as e:
            print(f"Detail service error: {str(e)}")
            return self._create_error_response(request_data, str(e))

    def _fetch_from_provider(self, request_data, context, cache_key, start_time):
        """
        Fetch flight details from provider and cache the results.
        """
        try:
            # Translate the client request data into the provider's expected format
            provider_payload = review_translate_client_to_provider(context, request_data)

            # Construct the API URL for the flight detail provider using the base URL from config
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/review"

            # Make the API call to the flight detail provider and get the response
            provider_response = make_provider_api_call(provider_api_url, provider_payload)

            # Translate the provider's response back into the format expected by the client
            client_response = review_translate_provider_to_client(request_data, provider_response, context)

            # Add the TUI back into the client response for reference
            client_response["TUI"] = context["TUI"]

            # Add performance metadata
            client_response.update({
                "cache_hit": False,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "data_source": "provider_direct",
                "cached_at": datetime.now(timezone.utc).isoformat()
            })

            # Store the client response in Redis cache with a TTL (Time To Live) of 5 minutes
            try:
                self.redis_client.set_cache(cache_key, client_response, config.cache_timer.get('FLIGHT_DETAIL'))
            except Exception as cache_error:
                print(f"Cache write error: {str(cache_error)}")

            # Return the client response containing flight details
            return client_response

        except Exception as e:
            print(f"Provider call error: {str(e)}")
            # Return error response
            return self._create_error_response(request_data, str(e))

    def _trigger_background_refresh_if_needed(self, request_data, cached_results, context, fare_id):
        """
        Trigger background refresh if cached data is getting stale.
        """
        try:
            cached_at_str = cached_results.get("cached_at")
            if cached_at_str:
                cached_at = datetime.fromisoformat(cached_at_str.replace('Z', '+00:00'))
                age_minutes = (datetime.now(timezone.utc) - cached_at).total_seconds() / 60

                # Refresh if data is older than 3 minutes (details are more volatile)
                if age_minutes > 3:
                    fetch_flight_detail_task.delay("RefreshProvider", request_data, context["TUI"], context, fare_id)

        except Exception as e:
            print(f"Background refresh trigger error: {str(e)}")

    def _create_error_response(self, request_data, error_message):
        """
        Create standardized error response for detail service.
        """
        return {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Flight detail service error"],
            "cache_hit": False,
            "data_source": "error",
            "error_details": error_message
        }

    def get_detail(self, request_data):
        """
        Enhanced retrieval of cached flight details based on the provided TUI.

        Args:
            request_data (dict): The request data containing the TUI.

        Returns:
            dict: The cached results or an error message if no results found.
        """
        # Extract the TUI (cache key) from the request data
        tui = request_data.get('TUI')

        if not tui:
            return self._create_error_response(request_data, "Missing TUI parameter")

        try:
            # Attempt to retrieve results from the cache using the TUI
            results = self.redis_client.get_cache(tui)

            # If no results are found in the cache
            if not results:
                return {
                    "TUI": tui,
                    "Completed": False,
                    "CeilingInfo": None,
                    "CurrencyCode": "INR",
                    "Notices": ["Flight details have expired. Please request fresh pricing."],
                    "Trips": None,
                    "Code": "404",
                    "Msg": ["Flight details not found or expired"]
                }

            # Add the TUI back into the results for reference and metadata
            results.update({
                "TUI": tui,
                "retrieved_at": datetime.now(timezone.utc).isoformat(),
                "data_source": "cache_retrieval"
            })

            # Return the cached results
            return results

        except Exception as e:
            print(f"Get detail error: {str(e)}")
            return self._create_error_response(request_data, str(e))