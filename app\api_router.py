from fastapi import APIRouter
from app.microservices.flight_service.api_router import router as flight_service_router
from app.microservices.auth_service.routes import router as auth_service_router
from app.microservices.booking_service.routes import router as booking_service_router
from app.microservices.dashboard_service.routers.booking import router as dashboard_booking_router

# Create an instance of APIRouter
api_router = APIRouter()

# Include the flight service router with an empty prefix
api_router.include_router(flight_service_router, prefix="")
api_router.include_router(auth_service_router, prefix="/auth")
api_router.include_router(booking_service_router, prefix="")
api_router.include_router(dashboard_booking_router, tags=["Dashboard"])
