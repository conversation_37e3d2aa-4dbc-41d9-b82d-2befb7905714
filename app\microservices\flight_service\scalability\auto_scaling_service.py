"""
Auto-scaling and load balancing service for dynamic resource management.
Provides intelligent scaling decisions based on real-time metrics and predictions.
"""

import asyncio
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

from app.microservices.flight_service.analytics.performance_service import performance_analytics
from app.microservices.flight_service.analytics.predictive_service import predictive_analytics
from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.database.connection_pool import db_connection_pool


class ScalingAction(Enum):
    """Types of scaling actions."""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    SCALE_OUT = "scale_out"  # Horizontal scaling
    SCALE_IN = "scale_in"   # Horizontal scaling
    OPTIMIZE = "optimize"
    NO_ACTION = "no_action"


class ResourceType(Enum):
    """Types of resources that can be scaled."""
    CPU = "cpu"
    MEMORY = "memory"
    DATABASE_CONNECTIONS = "database_connections"
    CACHE_SIZE = "cache_size"
    WORKER_PROCESSES = "worker_processes"
    LOAD_BALANCER = "load_balancer"


@dataclass
class ScalingRule:
    """Scaling rule configuration."""
    resource_type: ResourceType
    metric_name: str
    scale_up_threshold: float
    scale_down_threshold: float
    cooldown_period: int  # seconds
    max_instances: int
    min_instances: int
    scaling_factor: float = 1.5
    enabled: bool = True


@dataclass
class ScalingDecision:
    """Scaling decision result."""
    resource_type: ResourceType
    action: ScalingAction
    current_value: float
    target_value: float
    reason: str
    confidence: float
    estimated_impact: str
    implementation_time: int  # seconds


@dataclass
class LoadBalancerConfig:
    """Load balancer configuration."""
    algorithm: str  # round_robin, least_connections, weighted
    health_check_interval: int
    failure_threshold: int
    recovery_threshold: int
    sticky_sessions: bool = False


class AutoScalingService:
    """
    Auto-scaling service with intelligent resource management and load balancing.
    """
    
    def __init__(self):
        # Scaling rules
        self.scaling_rules: Dict[str, ScalingRule] = {}
        
        # Current resource state
        self.resource_state: Dict[ResourceType, Dict[str, Any]] = {}
        
        # Scaling history
        self.scaling_history: List[Dict[str, Any]] = []
        
        # Load balancer configuration
        self.load_balancer_config = LoadBalancerConfig(
            algorithm="round_robin",
            health_check_interval=30,
            failure_threshold=3,
            recovery_threshold=2
        )
        
        # Service configuration
        self.config = {
            "scaling_check_interval": 60,  # seconds
            "prediction_horizon": 300,     # 5 minutes
            "min_confidence_threshold": 0.7,
            "emergency_scaling_threshold": 0.9,
            "history_retention_hours": 24
        }
        
        # Service statistics
        self.service_stats = {
            "total_scaling_decisions": 0,
            "successful_scalings": 0,
            "failed_scalings": 0,
            "emergency_scalings": 0,
            "last_scaling_time": None,
            "active_rules": 0
        }
        
        # Initialize default scaling rules
        self._initialize_default_rules()
        
        # Initialize resource state
        self._initialize_resource_state()
        
        # Start auto-scaling loop
        self._scaling_task = None
        self._start_scaling_loop()
    
    def _initialize_default_rules(self):
        """Initialize default scaling rules."""
        self.scaling_rules = {
            "cpu_scaling": ScalingRule(
                resource_type=ResourceType.CPU,
                metric_name="cpu_utilization",
                scale_up_threshold=0.8,
                scale_down_threshold=0.3,
                cooldown_period=300,  # 5 minutes
                max_instances=10,
                min_instances=2,
                scaling_factor=1.5
            ),
            "memory_scaling": ScalingRule(
                resource_type=ResourceType.MEMORY,
                metric_name="memory_utilization",
                scale_up_threshold=0.85,
                scale_down_threshold=0.4,
                cooldown_period=300,
                max_instances=8,
                min_instances=2,
                scaling_factor=1.3
            ),
            "db_connection_scaling": ScalingRule(
                resource_type=ResourceType.DATABASE_CONNECTIONS,
                metric_name="database_connection_utilization",
                scale_up_threshold=0.9,
                scale_down_threshold=0.5,
                cooldown_period=180,  # 3 minutes
                max_instances=50,
                min_instances=5,
                scaling_factor=1.2
            ),
            "cache_scaling": ScalingRule(
                resource_type=ResourceType.CACHE_SIZE,
                metric_name="cache_memory_utilization",
                scale_up_threshold=0.9,
                scale_down_threshold=0.6,
                cooldown_period=600,  # 10 minutes
                max_instances=1024,  # MB
                min_instances=128,   # MB
                scaling_factor=1.5
            )
        }
        
        self.service_stats["active_rules"] = len([r for r in self.scaling_rules.values() if r.enabled])
    
    def _initialize_resource_state(self):
        """Initialize current resource state."""
        self.resource_state = {
            ResourceType.CPU: {
                "current_instances": 2,
                "utilization": 0.5,
                "last_scaled": None,
                "target_instances": 2
            },
            ResourceType.MEMORY: {
                "current_instances": 2,
                "utilization": 0.4,
                "last_scaled": None,
                "target_instances": 2
            },
            ResourceType.DATABASE_CONNECTIONS: {
                "current_instances": 10,
                "utilization": 0.3,
                "last_scaled": None,
                "target_instances": 10
            },
            ResourceType.CACHE_SIZE: {
                "current_instances": 256,  # MB
                "utilization": 0.7,
                "last_scaled": None,
                "target_instances": 256
            },
            ResourceType.WORKER_PROCESSES: {
                "current_instances": 4,
                "utilization": 0.6,
                "last_scaled": None,
                "target_instances": 4
            }
        }
    
    def _start_scaling_loop(self):
        """Start the auto-scaling loop."""
        if self._scaling_task is None or self._scaling_task.done():
            self._scaling_task = asyncio.create_task(self._scaling_loop())
    
    async def _scaling_loop(self):
        """Background auto-scaling loop."""
        while True:
            try:
                await asyncio.sleep(self.config["scaling_check_interval"])
                
                # Collect current metrics
                await self._collect_resource_metrics()
                
                # Make scaling decisions
                scaling_decisions = await self._make_scaling_decisions()
                
                # Execute scaling decisions
                for decision in scaling_decisions:
                    await self._execute_scaling_decision(decision)
                
                # Cleanup old history
                await self._cleanup_scaling_history()
                
            except Exception as e:
                print(f"Error in scaling loop: {str(e)}")
    
    async def _collect_resource_metrics(self):
        """Collect current resource utilization metrics."""
        try:
            # Get performance dashboard data
            dashboard_data = await performance_analytics.get_performance_dashboard()
            
            # Get database stats
            db_stats = db_connection_pool.get_performance_stats()
            
            # Get cache stats
            cache_stats = await flight_cache_service.get_cache_statistics()
            
            # Update resource state with current metrics
            # Note: In a real implementation, these would come from actual system monitoring
            
            # Simulate CPU utilization based on response times
            current_metrics = dashboard_data.get("current_metrics", {})
            avg_response_time = 0
            response_time_count = 0
            
            for metric_key, metric_data in current_metrics.items():
                if "response_time" in metric_key:
                    avg_response_time += metric_data.get("current_value", 0)
                    response_time_count += 1
            
            if response_time_count > 0:
                avg_response_time /= response_time_count
                # Convert response time to CPU utilization estimate
                cpu_utilization = min(0.95, avg_response_time / 1000)  # Normalize to 0-1
                self.resource_state[ResourceType.CPU]["utilization"] = cpu_utilization
            
            # Database connection utilization
            pool_stats = db_stats.get("pool_stats", {})
            active_connections = pool_stats.get("active_connections", 0)
            max_connections = 20  # Assumed max from pool config
            db_utilization = active_connections / max_connections if max_connections > 0 else 0
            self.resource_state[ResourceType.DATABASE_CONNECTIONS]["utilization"] = db_utilization
            
            # Cache utilization (simplified)
            cache_hit_rate = cache_stats.get("hit_rate_percentage", 0) / 100
            # Lower hit rate might indicate need for more cache
            cache_utilization = 1 - cache_hit_rate
            self.resource_state[ResourceType.CACHE_SIZE]["utilization"] = cache_utilization
            
        except Exception as e:
            print(f"Error collecting resource metrics: {str(e)}")
    
    async def _make_scaling_decisions(self) -> List[ScalingDecision]:
        """Make scaling decisions based on current metrics and predictions."""
        decisions = []
        
        try:
            current_time = time.time()
            
            for rule_name, rule in self.scaling_rules.items():
                if not rule.enabled:
                    continue
                
                resource_state = self.resource_state.get(rule.resource_type)
                if not resource_state:
                    continue
                
                # Check cooldown period
                last_scaled = resource_state.get("last_scaled")
                if last_scaled and (current_time - last_scaled) < rule.cooldown_period:
                    continue
                
                current_utilization = resource_state.get("utilization", 0)
                current_instances = resource_state.get("current_instances", 1)
                
                # Determine scaling action
                action = ScalingAction.NO_ACTION
                target_instances = current_instances
                reason = "No scaling needed"
                confidence = 1.0
                
                if current_utilization > rule.scale_up_threshold:
                    if current_instances < rule.max_instances:
                        action = ScalingAction.SCALE_UP
                        target_instances = min(
                            rule.max_instances,
                            int(current_instances * rule.scaling_factor)
                        )
                        reason = f"High utilization: {current_utilization:.2%} > {rule.scale_up_threshold:.2%}"
                        confidence = min(1.0, (current_utilization - rule.scale_up_threshold) * 2)
                
                elif current_utilization < rule.scale_down_threshold:
                    if current_instances > rule.min_instances:
                        action = ScalingAction.SCALE_DOWN
                        target_instances = max(
                            rule.min_instances,
                            int(current_instances / rule.scaling_factor)
                        )
                        reason = f"Low utilization: {current_utilization:.2%} < {rule.scale_down_threshold:.2%}"
                        confidence = min(1.0, (rule.scale_down_threshold - current_utilization) * 2)
                
                # Check for emergency scaling
                if current_utilization > self.config["emergency_scaling_threshold"]:
                    action = ScalingAction.SCALE_UP
                    target_instances = min(rule.max_instances, current_instances * 2)
                    reason = f"Emergency scaling: {current_utilization:.2%}"
                    confidence = 1.0
                    self.service_stats["emergency_scalings"] += 1
                
                # Create scaling decision
                if action != ScalingAction.NO_ACTION:
                    decision = ScalingDecision(
                        resource_type=rule.resource_type,
                        action=action,
                        current_value=current_instances,
                        target_value=target_instances,
                        reason=reason,
                        confidence=confidence,
                        estimated_impact=self._estimate_scaling_impact(rule.resource_type, action),
                        implementation_time=self._estimate_implementation_time(rule.resource_type, action)
                    )
                    
                    decisions.append(decision)
                    self.service_stats["total_scaling_decisions"] += 1
            
            # Use predictive analytics for proactive scaling
            predictive_decisions = await self._make_predictive_scaling_decisions()
            decisions.extend(predictive_decisions)
            
        except Exception as e:
            print(f"Error making scaling decisions: {str(e)}")
        
        return decisions
    
    async def _make_predictive_scaling_decisions(self) -> List[ScalingDecision]:
        """Make scaling decisions based on predictive analytics."""
        decisions = []
        
        try:
            # Get predictive insights
            insights = await predictive_analytics.get_predictive_insights()
            
            # Check capacity recommendations
            capacity_recommendations = insights.get("capacity_recommendations", [])
            
            for rec in capacity_recommendations:
                if rec.get("urgency") in ["high", "critical"]:
                    # Convert capacity recommendation to scaling decision
                    component = rec.get("component", "")
                    
                    if "response time" in component.lower():
                        resource_type = ResourceType.CPU
                    elif "cache" in component.lower():
                        resource_type = ResourceType.CACHE_SIZE
                    elif "database" in component.lower():
                        resource_type = ResourceType.DATABASE_CONNECTIONS
                    else:
                        continue
                    
                    decision = ScalingDecision(
                        resource_type=resource_type,
                        action=ScalingAction.SCALE_UP,
                        current_value=rec.get("current_capacity", 0),
                        target_value=rec.get("predicted_demand", 0),
                        reason=f"Predictive scaling: {rec.get('recommended_action', '')}",
                        confidence=0.8,  # Predictive confidence
                        estimated_impact=rec.get("estimated_impact", ""),
                        implementation_time=300  # 5 minutes
                    )
                    
                    decisions.append(decision)
            
        except Exception as e:
            print(f"Error making predictive scaling decisions: {str(e)}")
        
        return decisions
    
    async def _execute_scaling_decision(self, decision: ScalingDecision):
        """Execute a scaling decision."""
        try:
            print(f"Executing scaling decision: {decision.action.value} for {decision.resource_type.value}")
            print(f"Reason: {decision.reason}")
            print(f"Target: {decision.current_value} -> {decision.target_value}")
            
            # Update resource state
            resource_state = self.resource_state[decision.resource_type]
            resource_state["target_instances"] = decision.target_value
            resource_state["last_scaled"] = time.time()
            
            # Simulate scaling execution
            success = await self._simulate_scaling_execution(decision)
            
            if success:
                resource_state["current_instances"] = decision.target_value
                self.service_stats["successful_scalings"] += 1
                self.service_stats["last_scaling_time"] = time.time()
            else:
                self.service_stats["failed_scalings"] += 1
            
            # Record scaling history
            self.scaling_history.append({
                "timestamp": time.time(),
                "resource_type": decision.resource_type.value,
                "action": decision.action.value,
                "current_value": decision.current_value,
                "target_value": decision.target_value,
                "reason": decision.reason,
                "confidence": decision.confidence,
                "success": success
            })
            
        except Exception as e:
            print(f"Error executing scaling decision: {str(e)}")
            self.service_stats["failed_scalings"] += 1
    
    async def _simulate_scaling_execution(self, decision: ScalingDecision) -> bool:
        """Simulate scaling execution (in production, this would call actual scaling APIs)."""
        try:
            # Simulate execution time
            await asyncio.sleep(1)
            
            # Simulate success/failure (95% success rate)
            import random
            return random.random() > 0.05
            
        except Exception:
            return False
    
    def _estimate_scaling_impact(self, resource_type: ResourceType, action: ScalingAction) -> str:
        """Estimate the impact of a scaling action."""
        if action == ScalingAction.SCALE_UP:
            if resource_type == ResourceType.CPU:
                return "Improved response times and throughput"
            elif resource_type == ResourceType.MEMORY:
                return "Better memory utilization and reduced GC pressure"
            elif resource_type == ResourceType.DATABASE_CONNECTIONS:
                return "Reduced database connection contention"
            elif resource_type == ResourceType.CACHE_SIZE:
                return "Improved cache hit rate and performance"
        elif action == ScalingAction.SCALE_DOWN:
            return "Reduced resource costs with maintained performance"
        
        return "Optimized resource utilization"
    
    def _estimate_implementation_time(self, resource_type: ResourceType, action: ScalingAction) -> int:
        """Estimate implementation time in seconds."""
        if resource_type == ResourceType.CPU:
            return 180  # 3 minutes
        elif resource_type == ResourceType.MEMORY:
            return 120  # 2 minutes
        elif resource_type == ResourceType.DATABASE_CONNECTIONS:
            return 60   # 1 minute
        elif resource_type == ResourceType.CACHE_SIZE:
            return 300  # 5 minutes
        
        return 120  # Default 2 minutes
    
    async def _cleanup_scaling_history(self):
        """Clean up old scaling history."""
        cutoff_time = time.time() - (self.config["history_retention_hours"] * 3600)
        self.scaling_history = [
            entry for entry in self.scaling_history
            if entry["timestamp"] > cutoff_time
        ]
    
    def add_scaling_rule(self, rule_name: str, rule: ScalingRule):
        """Add a new scaling rule."""
        self.scaling_rules[rule_name] = rule
        if rule.enabled:
            self.service_stats["active_rules"] += 1
    
    def remove_scaling_rule(self, rule_name: str):
        """Remove a scaling rule."""
        if rule_name in self.scaling_rules:
            if self.scaling_rules[rule_name].enabled:
                self.service_stats["active_rules"] -= 1
            del self.scaling_rules[rule_name]
    
    def get_current_resource_state(self) -> Dict[str, Any]:
        """Get current resource state."""
        state = {}
        for resource_type, resource_data in self.resource_state.items():
            state[resource_type.value] = resource_data
        return state
    
    def get_scaling_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get scaling history for the specified time period."""
        cutoff_time = time.time() - (hours * 3600)
        return [
            entry for entry in self.scaling_history
            if entry["timestamp"] > cutoff_time
        ]
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get auto-scaling service statistics."""
        return {
            **self.service_stats,
            "scaling_rules_count": len(self.scaling_rules),
            "scaling_history_entries": len(self.scaling_history),
            "scaling_task_running": self._scaling_task is not None and not self._scaling_task.done(),
            "config": self.config
        }
    
    async def shutdown(self):
        """Shutdown the auto-scaling service."""
        if self._scaling_task:
            self._scaling_task.cancel()
        
        print("Auto-scaling service shutdown complete")


# Global auto-scaling service instance
auto_scaling_service = AutoScalingService()
