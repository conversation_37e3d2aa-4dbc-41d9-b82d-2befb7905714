"""
The core business logic for the hotel search service is implemented here. This file processes incoming search requests, interacts with providers, and utilizes the aggregator to compile results. It’s crucial for implementing the main functionality of our service.
"""
import time
from datetime import datetime, timezone

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service, CacheStrategy, generate_search_cache_key
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import flight_search_translate_client_to_provider
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import SearchProviderResponseTranslator
from app.microservices.flight_service.search_service.tasks import demo_search_availability_cache, fetch_flight_search_task
from app.microservices.flight_service.utils.provider_utils import make_provider_api_call
from app.microservices.flight_service import config


class FlightSearch:
    def __init__(self):
        self.cache_service = flight_cache_service
        self.redis_client = RedisCache.connection()

    def search(self, request_data):
        """
        Enhanced flight search with multi-layer caching and intelligent fallback.

        Args:
            request_data (dict): The request data containing search parameters.

        Returns:
            dict: The response from the flight provider or cached results.
        """
        start_time = time.time()

        try:
            # Step 1: Check multi-layer cache using the enhanced cache service
            cache_key = generate_search_cache_key(request_data)

            # Try to get from enhanced cache service (checks L1 and L2)
            cached_results = None
            try:
                # Since the cache service methods are async, we'll use the Redis client directly for now
                # In a full async implementation, this would be: cached_results = await self.cache_service.get_flight_search_results(request_data)
                cached_results = self.redis_client.get_cache(cache_key)
            except Exception as e:
                print(f"Cache lookup error: {str(e)}")

            if cached_results:
                # Cache hit - trigger background refresh if needed
                fetch_flight_search_task.delay("CachedProvider", request_data, cache_key)

                # Add performance metadata
                cached_results.update({
                    "sh_price": False,  # Indicates cached data
                    "cache_hit": True,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "cache"
                })

                return cached_results

            # Step 2: Cache miss - fetch from provider with fallback strategy
            return self._fetch_with_fallback_strategy(request_data, start_time)

        except Exception as e:
            print(f"Search error: {str(e)}")
            return self._create_error_response(request_data, str(e))

    def _fetch_with_fallback_strategy(self, request_data, start_time):
        """
        Fetch flight data with fallback strategy.
        """
        try:
            # Try the demo search availability cache (existing implementation)
            results = demo_search_availability_cache(request_data)

            if results and results.get("Trips"):
                # Add performance metadata
                results.update({
                    "sh_price": False,
                    "cache_hit": False,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "provider_direct"
                })

                return results

        except Exception as e:
            print(f"Provider error: {str(e)}")

        # Fallback to background task with immediate response
        return self._initiate_background_search(request_data, start_time)

    def _initiate_background_search(self, request_data, start_time):
        """
        Initiate background search and return immediate response.
        """
        cache_key = generate_search_cache_key(request_data)

        # Trigger background task
        fetch_flight_search_task.delay("BackgroundProvider", request_data, cache_key)

        # Return immediate response indicating search is in progress
        return {
            "TUI": cache_key,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": ["Search in progress. Please check back in a few moments."],
            "Trips": [],
            "Code": "202",  # Accepted - processing
            "Msg": ["Flight search initiated. Results will be available shortly."],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "background_task",
            "search_status": "processing"
        }

    def _create_error_response(self, request_data, error_message):
        """
        Create standardized error response.
        """
        return {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Internal server error occurred"],
            "sh_price": False,
            "cache_hit": False,
            "data_source": "error",
            "error_details": error_message
        }

    def search_list(self, request_data):
        """
        Enhanced search list retrieval with better error handling.

        Args:
            request_data (dict): The request data containing the TUI.

        Returns:
            dict: The cached results or an error message if no results found.
        """
        cache_key = request_data.get('TUI')

        if not cache_key:
            return self._create_error_response(request_data, "Missing TUI parameter")

        try:
            # Attempt to retrieve results from cache
            results = self.redis_client.get_cache(cache_key)

            if not results:
                return {
                    "TUI": cache_key,
                    "Completed": False,
                    "CeilingInfo": None,
                    "CurrencyCode": "INR",
                    "Notices": ["Search results have expired. Please perform a new search."],
                    "Trips": None,
                    "Code": "404",
                    "Msg": ["Search results not found or expired"]
                }

            # Add metadata and return results
            results.update({
                "TUI": cache_key,
                "sh_price": True,
                "retrieved_at": datetime.now(timezone.utc).isoformat(),
                "data_source": "cache_retrieval"
            })

            return results

        except Exception as e:
            print(f"Search list error: {str(e)}")
            return self._create_error_response(request_data, str(e))
