from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Depends
from .services import BookingService
from .schemas import BookingRequest
from fastapi.responses import JSONResponse
from .auth_middleware import get_current_user

router = APIRouter()


@router.post("/create-booking/")
async def create_booking(request: BookingRequest, current_user: dict = Depends(get_current_user)):
    """
    Creates a booking based on the validated BookingRequest schema.
    """
    try:
        booking_data = request.model_dump()
        booking_data["user_id"] = current_user["payload"]["user_id"]
        result = await BookingService.create_master_booking(booking_data)

        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get-booking/{booking_reference}")
async def get_booking(booking_reference: str):
    try:
        booking_details = await BookingService.get_booking_details(booking_reference)

        if "detail" in booking_details:
            raise HTTPException(status_code=404, detail=booking_details["detail"])

        return JSONResponse(content=booking_details)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/user-bookings/")
async def get_user_bookings(current_user: dict = Depends(get_current_user)):
    """
    Retrieve all bookings for the currently authenticated user.
    """
    try:
        user_id = current_user["payload"]["user_id"]
        bookings = await BookingService.get_bookings_by_user(user_id)

        if "detail" in bookings:
            raise HTTPException(status_code=404, detail=bookings["detail"])

        return bookings
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get-all-bookings/")
async def get_all_bookings():
    """
    Retrieve all bookings with detailed information from the database.
    """
    try:
        bookings = await BookingService.get_all_bookings()
        if "detail" in bookings:
            raise HTTPException(status_code=404, detail=bookings["detail"])

        return bookings
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))