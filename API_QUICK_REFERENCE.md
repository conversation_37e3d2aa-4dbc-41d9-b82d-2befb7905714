# Flight Booking API - Quick Reference Card

## 🚀 Quick Start

### Authentication
```http
X-API-Key: your-api-key-here
# OR
Authorization: Bearer your-jwt-token
```

### Base URLs
- **Production**: `https://api.flightbooking.com/v4`
- **Local Dev**: `http://localhost:8000/apis`

## 📋 Essential Endpoints

### Core Flight Operations
```bash
# Search Flights
POST /flight-search
{
  "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-02-15"}],
  "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "E", "FareType": "REGULAR"
}

# Get Pricing Details
POST /flight-pricing
{
  "FareId": "FARE_001_E_REG",
  "ADT": 1, "CHD": 0, "INF": 0
}

# Retrieve Cached Results
POST /flight-search-list
{"TUI": "SEARCH_12345_20240115_143022"}
```

### Enhanced Async Operations
```bash
# Async Search with Optimization
POST /async/search
{
  "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-02-15"}],
  "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "E", "FareType": "REGULAR",
  "async": true
}

# Performance Monitoring
GET /performance/search
GET /performance/provider
```

### Cache Management (Admin)
```bash
# Cache Statistics
GET /cache/statistics

# Trigger Cache Warming
POST /cache/warm
{
  "routes": [{"from": "DEL", "to": "BOM", "dates": ["2024-02-15"]}],
  "max_routes": 20, "priority": "high"
}
```

### Advanced Monitoring (Admin)
```bash
# Real-time Dashboard (WebSocket)
WS /advanced/dashboard/realtime

# Generate Reports
POST /advanced/reporting/generate-report
{
  "report_type": "daily_summary",
  "format": "html",
  "hours": 24
}

# Predictive Forecasting
POST /advanced/analytics/forecast
{
  "metric_name": "response_times",
  "hours_ahead": 24
}

# System Health
GET /advanced/monitoring/comprehensive-status
```

## 🔧 Performance Headers

### Request Optimization
```http
# Enable async processing
X-Async-Processing: true

# Cache preferences
Cache-Control: max-age=300
If-None-Match: "etag-value"
```

### Response Indicators
```http
# Cache status
X-Cache-Status: HIT|MISS|REFRESH
X-Cache-Layer: L1_MEMORY|L2_REDIS|L3_PERSISTENT

# Performance metrics
X-Response-Time: 45
X-Performance-Score: 95

# Circuit breaker status
X-Circuit-Breaker-Status: CLOSED|OPEN|HALF_OPEN
X-Request-Deduplication: true|false

# Rate limiting
X-RateLimit-Remaining: 995
X-RateLimit-Reset: **********
```

## 📊 Response Formats

### Success Response
```json
{
  "TUI": "SEARCH_12345_20240115_143022",
  "status": "success",
  "data_source": "cache",
  "cache_hit": true,
  "response_time_ms": 45.2,
  "Results": [...]
}
```

### Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": "Required field 'From' is missing",
    "field_errors": [
      {"field": "From", "message": "This field is required", "code": "REQUIRED"}
    ]
  }
}
```

### WebSocket Messages
```json
// Subscribe to metrics
{"type": "subscribe", "subscription": "system_metrics"}

// Real-time metric update
{
  "type": "real_time_metric",
  "subscription": "system_metrics",
  "metric": {
    "metric_id": "response_time_**********",
    "timestamp": **********.123,
    "service": "search_service",
    "metric_type": "response_time",
    "value": 245.5
  }
}

// Performance alert
{
  "type": "performance_alert",
  "alert": {
    "severity": "warning",
    "message": "Response time above threshold",
    "component": "search_service"
  }
}
```

## 🎯 Common Use Cases

### 1. Basic Flight Search Flow
```bash
# 1. Search for flights
curl -X POST "/flight-search" -d '{...search params...}'
# Response: {"TUI": "SEARCH_123", "Results": [...]}

# 2. Get detailed pricing
curl -X POST "/flight-pricing" -d '{"FareId": "FARE_001"}'
# Response: {"TUI": "PRICING_456", "FlightDetails": {...}}

# 3. Retrieve cached results later
curl -X POST "/flight-search-list" -d '{"TUI": "SEARCH_123"}'
```

### 2. Performance Monitoring Setup
```bash
# 1. Check cache performance
curl -X GET "/cache/statistics"

# 2. Monitor service performance
curl -X GET "/performance/search"

# 3. Set up real-time monitoring
wscat -c "ws://localhost:8000/apis/advanced/dashboard/realtime"
```

### 3. Predictive Analytics Workflow
```bash
# 1. Generate forecast
curl -X POST "/advanced/analytics/forecast" \
  -d '{"metric_name": "response_times", "hours_ahead": 24}'

# 2. Get capacity recommendations
curl -X GET "/advanced/analytics/capacity-recommendations"

# 3. Check for anomalies
curl -X GET "/advanced/analytics/anomalies"
```

## ⚡ Performance Tips

### Caching Strategy
- **Search Results**: 15-minute TTL
- **Pricing Data**: 5-minute TTL with background refresh
- **Static Data**: 24-hour TTL
- **Use TUI**: Always store and reuse Transaction IDs

### Optimization Features
- **Request Deduplication**: 60-80% reduction in redundant calls
- **Circuit Breakers**: Automatic fallback to cached data
- **Multi-layer Caching**: 95%+ hit rate for popular routes
- **Async Processing**: 70-85% faster response times

### Rate Limiting
- **Standard**: 1000 requests/hour
- **Premium**: 10000 requests/hour
- **Burst Limit**: 20 requests/10 seconds
- **Monitor Headers**: Check `X-RateLimit-Remaining`

## 🚨 Error Handling

### Common Error Codes
| Code | Description | Action |
|------|-------------|---------|
| `VALIDATION_ERROR` | Invalid parameters | Check request format |
| `RATE_LIMIT_EXCEEDED` | Too many requests | Wait for reset time |
| `TUI_NOT_FOUND` | Transaction expired | Perform new search |
| `CIRCUIT_BREAKER_OPEN` | Service unavailable | Use cached data |
| `INVALID_API_KEY` | Authentication failed | Check API key |

### Retry Strategy
```javascript
const retryWithBackoff = async (request, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await request();
    } catch (error) {
      if (error.code === 'RATE_LIMIT_EXCEEDED') {
        const delay = Math.pow(2, i) * 1000; // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
};
```

## 🔍 Monitoring & Debugging

### Health Check Endpoints
```bash
# System health
GET /advanced/monitoring/comprehensive-status

# Service-specific health
GET /cache/health
GET /performance/search
GET /admin/database/stats
```

### Debug Headers
```http
# Request debugging
X-Debug-Mode: true
X-Trace-Id: custom-trace-id

# Response debugging
X-Debug-Info: cache-miss-reason
X-Execution-Path: search->cache->provider
X-Query-Count: 3
```

### Performance Metrics
- **Response Time**: Target <500ms (95th percentile)
- **Cache Hit Rate**: Target >90%
- **Error Rate**: Target <1%
- **Availability**: Target 99.9%

## 📱 SDK Examples

### JavaScript/Node.js
```javascript
const FlightAPI = require('@flightbooking/api-client');
const client = new FlightAPI({apiKey: 'your-key'});

const results = await client.search({
  trips: [{from: 'DEL', to: 'BOM', date: '2024-02-15'}],
  passengers: {adults: 1, children: 0, infants: 0},
  cabin: 'E'
});
```

### Python
```python
from flightbooking import FlightAPI

client = FlightAPI(api_key='your-key')
results = client.search(
    trips=[{'from': 'DEL', 'to': 'BOM', 'date': '2024-02-15'}],
    passengers={'adults': 1, 'children': 0, 'infants': 0},
    cabin='E'
)
```

### cURL Template
```bash
#!/bin/bash
API_KEY="your-api-key"
BASE_URL="https://api.flightbooking.com/v4"

curl -X POST "$BASE_URL/flight-search" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-02-15"}],
    "ADT": 1, "CHD": 0, "INF": 0,
    "Cabin": "E", "FareType": "REGULAR"
  }'
```

## 🔗 Quick Links

- **Interactive Docs**: https://docs.flightbooking.com
- **Developer Portal**: https://portal.flightbooking.com
- **Status Page**: https://status.flightbooking.com
- **Support**: <EMAIL>

## 📋 Checklist for Integration

- [ ] Obtain API credentials
- [ ] Test in sandbox environment
- [ ] Implement error handling
- [ ] Set up monitoring
- [ ] Configure rate limiting
- [ ] Test WebSocket connections
- [ ] Implement caching strategy
- [ ] Set up alerting
- [ ] Performance testing
- [ ] Production deployment

---

**💡 Pro Tip**: Start with basic endpoints, then gradually add optimization features like async processing, caching, and real-time monitoring for maximum performance gains.
