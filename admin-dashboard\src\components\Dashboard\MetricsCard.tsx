import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  useTheme,
  alpha,
  Skeleton,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
} from '@mui/icons-material';

interface MetricsCardProps {
  title: string;
  value: string | number;
  unit?: string;
  subtitle?: string;
  trend?: {
    direction: 'up' | 'down' | 'flat';
    value: number;
    label?: string;
  };
  status?: 'success' | 'warning' | 'error' | 'info';
  progress?: {
    value: number;
    max: number;
    label?: string;
  };
  icon?: React.ReactElement;
  loading?: boolean;
  onClick?: () => void;
}

const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  unit,
  subtitle,
  trend,
  status,
  progress,
  icon,
  loading = false,
  onClick,
}) => {
  const theme = useTheme();

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'info':
        return theme.palette.info.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const getTrendIcon = (): React.ReactElement | undefined => {
    if (!trend) return undefined;

    switch (trend.direction) {
      case 'up':
        return <TrendingUpIcon sx={{ fontSize: 16 }} />;
      case 'down':
        return <TrendingDownIcon sx={{ fontSize: 16 }} />;
      case 'flat':
        return <TrendingFlatIcon sx={{ fontSize: 16 }} />;
      default:
        return undefined;
    }
  };

  const getTrendColor = () => {
    if (!trend) return theme.palette.text.secondary;

    switch (trend.direction) {
      case 'up':
        return theme.palette.success.main;
      case 'down':
        return theme.palette.error.main;
      case 'flat':
        return theme.palette.warning.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  if (loading) {
    return (
      <Card
        sx={{
          height: '100%',
          cursor: onClick ? 'pointer' : 'default',
          transition: 'all 0.2s ease-in-out',
          '&:hover': onClick ? {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
          } : {},
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
            <Skeleton variant="text" width="60%" height={20} />
          </Box>
          <Skeleton variant="text" width="40%" height={32} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="80%" height={16} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={4} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      sx={{
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease-in-out',
        border: `1px solid ${alpha(getStatusColor(), 0.2)}`,
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[8],
          borderColor: alpha(getStatusColor(), 0.4),
        } : {},
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon && (
            <Box
              sx={{
                mr: 1,
                color: getStatusColor(),
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {icon}
            </Box>
          )}
          <Typography
            variant="subtitle2"
            color="text.secondary"
            sx={{ fontWeight: 500, flex: 1 }}
          >
            {title}
          </Typography>
          {status && (
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: getStatusColor(),
              }}
            />
          )}
        </Box>

        {/* Main Value */}
        <Box sx={{ mb: 1 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: theme.palette.text.primary,
              lineHeight: 1.2,
            }}
          >
            {formatValue(value)}
            {unit && (
              <Typography
                component="span"
                variant="h6"
                sx={{
                  ml: 0.5,
                  color: theme.palette.text.secondary,
                  fontWeight: 400,
                }}
              >
                {unit}
              </Typography>
            )}
          </Typography>
        </Box>

        {/* Subtitle */}
        {subtitle && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 2 }}
          >
            {subtitle}
          </Typography>
        )}

        {/* Trend */}
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Chip
              icon={getTrendIcon()}
              label={`${trend.value > 0 ? '+' : ''}${trend.value}${trend.label || '%'}`}
              size="small"
              sx={{
                backgroundColor: alpha(getTrendColor(), 0.1),
                color: getTrendColor(),
                border: `1px solid ${alpha(getTrendColor(), 0.2)}`,
                '& .MuiChip-icon': {
                  color: getTrendColor(),
                },
              }}
            />
          </Box>
        )}

        {/* Progress */}
        {progress && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                {progress.label || 'Progress'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {progress.value}/{progress.max}
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={(progress.value / progress.max) * 100}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: alpha(getStatusColor(), 0.1),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: getStatusColor(),
                  borderRadius: 3,
                },
              }}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default MetricsCard;
