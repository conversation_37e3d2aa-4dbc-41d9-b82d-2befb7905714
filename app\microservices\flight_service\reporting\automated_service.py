"""
Automated reporting service with scheduled reports and notifications.
Provides comprehensive system reports, performance summaries, and automated alerts.
"""

import asyncio
import time
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON>Base
from email import encoders
import io
import csv

from app.config import RedisCache
from app.microservices.flight_service.analytics.performance_service import performance_analytics
from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.cache_warming.service import cache_warming_service
from app.microservices.flight_service.database.connection_pool import db_connection_pool


class ReportType(Enum):
    """Types of automated reports."""
    DAILY_SUMMARY = "daily_summary"
    WEEKLY_PERFORMANCE = "weekly_performance"
    MONTHLY_ANALYTICS = "monthly_analytics"
    ALERT_DIGEST = "alert_digest"
    SYSTEM_HEALTH = "system_health"
    CAPACITY_PLANNING = "capacity_planning"


class ReportFormat(Enum):
    """Report output formats."""
    JSON = "json"
    HTML = "html"
    CSV = "csv"
    PDF = "pdf"


@dataclass
class ReportSchedule:
    """Report scheduling configuration."""
    report_type: ReportType
    schedule_cron: str  # Cron-like schedule
    recipients: List[str]
    format: ReportFormat
    enabled: bool = True
    last_run: Optional[float] = None
    next_run: Optional[float] = None


@dataclass
class ReportTemplate:
    """Report template configuration."""
    name: str
    description: str
    sections: List[str]
    metrics: List[str]
    charts: List[str]
    custom_queries: List[str] = field(default_factory=list)


class AutomatedReportingService:
    """
    Automated reporting service with scheduling, templates, and notifications.
    """
    
    def __init__(self):
        self.redis_client = RedisCache.connection()
        
        # Report schedules
        self.schedules: Dict[str, ReportSchedule] = {}
        
        # Report templates
        self.templates: Dict[str, ReportTemplate] = {}
        
        # Service configuration
        self.config = {
            "smtp_server": "localhost",
            "smtp_port": 587,
            "smtp_username": "",
            "smtp_password": "",
            "from_email": "<EMAIL>",
            "report_retention_days": 30,
            "max_report_size_mb": 10
        }
        
        # Service statistics
        self.service_stats = {
            "total_reports_generated": 0,
            "reports_sent": 0,
            "failed_reports": 0,
            "last_report_time": None,
            "active_schedules": 0
        }
        
        # Initialize default schedules and templates
        self._initialize_default_schedules()
        self._initialize_default_templates()
        
        # Start scheduler
        self._scheduler_task = None
        self._start_scheduler()
    
    def _initialize_default_schedules(self):
        """Initialize default report schedules."""
        self.schedules = {
            "daily_summary": ReportSchedule(
                report_type=ReportType.DAILY_SUMMARY,
                schedule_cron="0 8 * * *",  # Daily at 8 AM
                recipients=["<EMAIL>"],
                format=ReportFormat.HTML,
                enabled=True
            ),
            "weekly_performance": ReportSchedule(
                report_type=ReportType.WEEKLY_PERFORMANCE,
                schedule_cron="0 9 * * 1",  # Monday at 9 AM
                recipients=["<EMAIL>", "<EMAIL>"],
                format=ReportFormat.HTML,
                enabled=True
            ),
            "system_health": ReportSchedule(
                report_type=ReportType.SYSTEM_HEALTH,
                schedule_cron="0 */6 * * *",  # Every 6 hours
                recipients=["<EMAIL>"],
                format=ReportFormat.JSON,
                enabled=True
            )
        }
        
        self.service_stats["active_schedules"] = len([s for s in self.schedules.values() if s.enabled])
    
    def _initialize_default_templates(self):
        """Initialize default report templates."""
        self.templates = {
            "daily_summary": ReportTemplate(
                name="Daily Performance Summary",
                description="Daily overview of system performance and key metrics",
                sections=["executive_summary", "performance_metrics", "alerts", "recommendations"],
                metrics=["response_time", "cache_hit_rate", "error_rate", "throughput"],
                charts=["response_time_trend", "cache_performance", "error_distribution"]
            ),
            "weekly_performance": ReportTemplate(
                name="Weekly Performance Analysis",
                description="Comprehensive weekly performance analysis with trends",
                sections=["executive_summary", "performance_trends", "capacity_analysis", "optimization_opportunities"],
                metrics=["avg_response_time", "peak_throughput", "cache_efficiency", "database_performance"],
                charts=["weekly_trends", "capacity_utilization", "performance_comparison"]
            ),
            "system_health": ReportTemplate(
                name="System Health Report",
                description="Current system health status and component analysis",
                sections=["health_overview", "component_status", "alerts", "recommendations"],
                metrics=["system_health_score", "component_scores", "alert_count"],
                charts=["health_trends", "component_comparison"]
            )
        }
    
    def _start_scheduler(self):
        """Start the report scheduler."""
        if self._scheduler_task is None or self._scheduler_task.done():
            self._scheduler_task = asyncio.create_task(self._scheduler_loop())
    
    async def _scheduler_loop(self):
        """Background scheduler loop."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = time.time()
                
                for schedule_id, schedule in self.schedules.items():
                    if schedule.enabled and self._should_run_report(schedule, current_time):
                        await self._execute_scheduled_report(schedule_id, schedule)
                        
            except Exception as e:
                print(f"Error in scheduler loop: {str(e)}")
    
    def _should_run_report(self, schedule: ReportSchedule, current_time: float) -> bool:
        """Check if a report should be run based on its schedule."""
        # Simplified scheduling logic - in production, use a proper cron parser
        if schedule.last_run is None:
            return True
        
        # Basic time-based scheduling
        time_since_last = current_time - schedule.last_run
        
        if "daily" in schedule.schedule_cron.lower():
            return time_since_last >= 86400  # 24 hours
        elif "weekly" in schedule.schedule_cron.lower():
            return time_since_last >= 604800  # 7 days
        elif "*/6" in schedule.schedule_cron:  # Every 6 hours
            return time_since_last >= 21600
        
        return False
    
    async def _execute_scheduled_report(self, schedule_id: str, schedule: ReportSchedule):
        """Execute a scheduled report."""
        try:
            print(f"Executing scheduled report: {schedule_id}")
            
            # Generate report
            report_data = await self.generate_report(schedule.report_type, schedule.format)
            
            # Send report
            if schedule.recipients:
                await self._send_report(report_data, schedule)
            
            # Update schedule
            schedule.last_run = time.time()
            self.service_stats["total_reports_generated"] += 1
            self.service_stats["last_report_time"] = time.time()
            
            print(f"Scheduled report completed: {schedule_id}")
            
        except Exception as e:
            self.service_stats["failed_reports"] += 1
            print(f"Error executing scheduled report {schedule_id}: {str(e)}")
    
    async def generate_report(self, report_type: ReportType, format: ReportFormat) -> Dict[str, Any]:
        """
        Generate a report of the specified type and format.
        
        Args:
            report_type: Type of report to generate
            format: Output format for the report
            
        Returns:
            Generated report data
        """
        try:
            # Get report template
            template = self.templates.get(report_type.value)
            if not template:
                raise ValueError(f"No template found for report type: {report_type.value}")
            
            # Collect data based on report type
            if report_type == ReportType.DAILY_SUMMARY:
                report_data = await self._generate_daily_summary()
            elif report_type == ReportType.WEEKLY_PERFORMANCE:
                report_data = await self._generate_weekly_performance()
            elif report_type == ReportType.SYSTEM_HEALTH:
                report_data = await self._generate_system_health_report()
            elif report_type == ReportType.ALERT_DIGEST:
                report_data = await self._generate_alert_digest()
            else:
                raise ValueError(f"Unsupported report type: {report_type.value}")
            
            # Format report
            formatted_report = await self._format_report(report_data, template, format)
            
            return {
                "report_type": report_type.value,
                "format": format.value,
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "template": template.name,
                "data": formatted_report
            }
            
        except Exception as e:
            print(f"Error generating report: {str(e)}")
            raise
    
    async def _generate_daily_summary(self) -> Dict[str, Any]:
        """Generate daily summary report data."""
        # Get performance data for the last 24 hours
        performance_report = await performance_analytics.generate_performance_report(24)
        dashboard_data = await performance_analytics.get_performance_dashboard()
        
        # Get cache statistics
        cache_stats = await flight_cache_service.get_cache_statistics()
        
        # Get database statistics
        db_stats = db_connection_pool.get_performance_stats()
        
        # Calculate key metrics
        system_health = dashboard_data.get("system_health", {})
        
        return {
            "summary": {
                "date": datetime.now(timezone.utc).strftime("%Y-%m-%d"),
                "system_health_score": system_health.get("overall_score", 0),
                "total_requests": cache_stats.get("total_requests", 0),
                "cache_hit_rate": cache_stats.get("hit_rate_percentage", 0),
                "avg_response_time": "N/A",  # Would be calculated from metrics
                "error_count": 0  # Would be calculated from error metrics
            },
            "performance_metrics": performance_report.get("services", {}),
            "cache_performance": cache_stats,
            "database_performance": db_stats,
            "system_health": system_health,
            "alerts": dashboard_data.get("recent_alerts", []),
            "recommendations": self._generate_recommendations(dashboard_data)
        }
    
    async def _generate_weekly_performance(self) -> Dict[str, Any]:
        """Generate weekly performance report data."""
        # Get performance data for the last 7 days
        weekly_report = await performance_analytics.generate_performance_report(168)  # 7 days
        
        return {
            "period": "7 days",
            "performance_trends": weekly_report.get("services", {}),
            "capacity_analysis": {
                "peak_usage": "N/A",  # Would be calculated from metrics
                "average_usage": "N/A",
                "growth_rate": "N/A"
            },
            "optimization_opportunities": [
                "Consider increasing cache TTL for stable data",
                "Optimize database queries with high execution time",
                "Implement additional caching layers for frequently accessed data"
            ]
        }
    
    async def _generate_system_health_report(self) -> Dict[str, Any]:
        """Generate system health report data."""
        dashboard_data = await performance_analytics.get_performance_dashboard()
        system_health = dashboard_data.get("system_health", {})
        
        return {
            "health_overview": {
                "overall_status": system_health.get("overall_status", "unknown"),
                "overall_score": system_health.get("overall_score", 0),
                "last_calculated": system_health.get("last_calculated", time.time())
            },
            "component_status": system_health.get("component_health", {}),
            "alerts": dashboard_data.get("recent_alerts", []),
            "recommendations": self._generate_health_recommendations(system_health)
        }
    
    async def _generate_alert_digest(self) -> Dict[str, Any]:
        """Generate alert digest report data."""
        # This would typically fetch alerts from a logging system
        return {
            "alert_summary": {
                "total_alerts": 0,
                "critical_alerts": 0,
                "warning_alerts": 0,
                "resolved_alerts": 0
            },
            "top_alerts": [],
            "alert_trends": {},
            "recommendations": []
        }
    
    def _generate_recommendations(self, dashboard_data: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on dashboard data."""
        recommendations = []
        
        system_health = dashboard_data.get("system_health", {})
        overall_score = system_health.get("overall_score", 1.0)
        
        if overall_score < 0.8:
            recommendations.append("System health is below optimal. Review component performance.")
        
        component_health = system_health.get("component_health", {})
        for component, health in component_health.items():
            if health.get("score", 1.0) < 0.7:
                recommendations.append(f"Component '{component}' needs attention - score: {health.get('score', 0):.2f}")
        
        if not recommendations:
            recommendations.append("System is performing well. Continue monitoring.")
        
        return recommendations
    
    def _generate_health_recommendations(self, system_health: Dict[str, Any]) -> List[str]:
        """Generate health-specific recommendations."""
        recommendations = []
        
        overall_score = system_health.get("overall_score", 1.0)
        
        if overall_score < 0.6:
            recommendations.append("Critical: System health is poor. Immediate attention required.")
        elif overall_score < 0.8:
            recommendations.append("Warning: System health is below optimal. Review and optimize.")
        else:
            recommendations.append("System health is good. Continue monitoring.")
        
        return recommendations
    
    async def _format_report(self, data: Dict[str, Any], template: ReportTemplate, format: ReportFormat) -> Any:
        """Format report data according to template and format."""
        if format == ReportFormat.JSON:
            return data
        elif format == ReportFormat.HTML:
            return self._format_html_report(data, template)
        elif format == ReportFormat.CSV:
            return self._format_csv_report(data, template)
        else:
            return data  # Default to JSON
    
    def _format_html_report(self, data: Dict[str, Any], template: ReportTemplate) -> str:
        """Format report as HTML."""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{template.name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e9e9e9; border-radius: 3px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{template.name}</h1>
                <p>{template.description}</p>
                <p>Generated: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
            </div>
        """
        
        # Add sections based on template
        for section in template.sections:
            if section in data:
                html += f'<div class="section"><h2>{section.replace("_", " ").title()}</h2>'
                
                section_data = data[section]
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        html += f'<div class="metric"><strong>{key}:</strong> {value}</div>'
                elif isinstance(section_data, list):
                    html += '<ul>'
                    for item in section_data:
                        html += f'<li>{item}</li>'
                    html += '</ul>'
                else:
                    html += f'<p>{section_data}</p>'
                
                html += '</div>'
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def _format_csv_report(self, data: Dict[str, Any], template: ReportTemplate) -> str:
        """Format report as CSV."""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([template.name])
        writer.writerow([f"Generated: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}"])
        writer.writerow([])
        
        # Write data sections
        for section in template.sections:
            if section in data:
                writer.writerow([section.replace("_", " ").title()])
                
                section_data = data[section]
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        writer.writerow([key, value])
                elif isinstance(section_data, list):
                    for item in section_data:
                        writer.writerow([item])
                
                writer.writerow([])
        
        return output.getvalue()
    
    async def _send_report(self, report_data: Dict[str, Any], schedule: ReportSchedule):
        """Send report via email."""
        try:
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.config["from_email"]
            msg['To'] = ", ".join(schedule.recipients)
            msg['Subject'] = f"Automated Report: {schedule.report_type.value.replace('_', ' ').title()}"
            
            # Email body
            body = f"""
            Automated report generated at {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}
            
            Report Type: {schedule.report_type.value}
            Format: {schedule.format.value}
            
            Please find the report attached or embedded below.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Attach or embed report
            if schedule.format == ReportFormat.HTML:
                msg.attach(MIMEText(report_data["data"], 'html'))
            else:
                # Attach as file
                attachment = MIMEBase('application', 'octet-stream')
                attachment.set_payload(json.dumps(report_data["data"], indent=2))
                encoders.encode_base64(attachment)
                attachment.add_header(
                    'Content-Disposition',
                    f'attachment; filename="report_{schedule.report_type.value}_{int(time.time())}.{schedule.format.value}"'
                )
                msg.attach(attachment)
            
            # Send email (simplified - in production, use proper SMTP configuration)
            print(f"Report would be sent to: {schedule.recipients}")
            print(f"Subject: {msg['Subject']}")
            
            self.service_stats["reports_sent"] += 1
            
        except Exception as e:
            print(f"Error sending report: {str(e)}")
            raise
    
    def add_schedule(self, schedule_id: str, schedule: ReportSchedule):
        """Add a new report schedule."""
        self.schedules[schedule_id] = schedule
        if schedule.enabled:
            self.service_stats["active_schedules"] += 1
    
    def remove_schedule(self, schedule_id: str):
        """Remove a report schedule."""
        if schedule_id in self.schedules:
            if self.schedules[schedule_id].enabled:
                self.service_stats["active_schedules"] -= 1
            del self.schedules[schedule_id]
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get reporting service statistics."""
        return {
            **self.service_stats,
            "total_schedules": len(self.schedules),
            "templates_available": len(self.templates),
            "scheduler_running": self._scheduler_task is not None and not self._scheduler_task.done()
        }
    
    async def shutdown(self):
        """Shutdown the reporting service."""
        if self._scheduler_task:
            self._scheduler_task.cancel()
        
        print("Automated reporting service shutdown complete")


# Global automated reporting service instance
automated_reporting = AutomatedReportingService()
