# 🌐 Local Network Access Setup Guide

This guide will help you run the Fast Travel Backend API server so it can be accessed from other devices on your local network (phones, tablets, other computers).

## 🚀 Quick Start

### Option 1: Using the Automated Script (Recommended)

**Windows (Command Prompt):**
```bash
start_server.bat
```

**Windows (PowerShell):**
```powershell
.\start_server.ps1
```

**Python (Any OS):**
```bash
python run_server_local_network.py
```

### Option 2: Manual Start
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 📋 Prerequisites

### Required Software
- **Python 3.8+** installed and in PATH
- **pip** package manager
- **Git** (for cloning the repository)

### Required Services
- **Redis Server** (for caching)
- **MySQL Server** (for database)

### Install Dependencies
```bash
# Install Python dependencies
pip install -r requirements.txt

# Or install manually
pip install fastapi uvicorn redis sqlalchemy pymysql aiohttp celery
```

## 🔧 Configuration Steps

### 1. Environment Setup
Create a `.env` file in the project root:
```env
# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost/database_name

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# API Configuration
ORIGIN=http://localhost:3000,http://*************:3000
DEBUG=True

# Performance Configuration
FLIGHT_SEARCH_CACHE_TIMER=1800
MEMORY_CACHE_MAX_SIZE=10000
RESPONSE_TIME_TARGET_MS=3000
REQUEST_DEDUPLICATION_ENABLED=true
```

### 2. Windows Firewall Configuration

**Automatic (Run as Administrator):**
```powershell
# Allow Python through firewall
New-NetFirewallRule -DisplayName "Python Server" -Direction Inbound -Protocol TCP -LocalPort 8000 -Action Allow
```

**Manual Configuration:**
1. Open **Windows Defender Firewall**
2. Click **"Allow an app or feature through Windows Defender Firewall"**
3. Click **"Change Settings"** then **"Allow another app"**
4. Browse and select **Python.exe** (usually in `C:\Python3X\python.exe`)
5. Check both **"Private"** and **"Public"** networks
6. Click **OK**

### 3. Network Discovery

**Find Your Local IP Address:**

**Windows:**
```cmd
ipconfig | findstr IPv4
```

**PowerShell:**
```powershell
(Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi").IPAddress
```

**Linux/Mac:**
```bash
hostname -I
```

## 🌐 Accessing the Server

### From Your Computer
- **Main API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### From Other Devices on Network
Replace `*************` with your actual local IP:

- **Main API**: http://*************:8000
- **API Documentation**: http://*************:8000/docs
- **Health Check**: http://*************:8000/health
- **Network Info**: http://*************:8000/network-info

### Optimized Endpoints
- **Optimized Search**: http://*************:8000/apis/optimized/search/optimized
- **Performance Stats**: http://*************:8000/apis/optimized/search/performance/stats

## 📱 Testing Network Access

### From Another Device
1. **Connect to the same Wi-Fi network**
2. **Open a web browser**
3. **Navigate to**: `http://YOUR_LOCAL_IP:8000/health`
4. **You should see**: `{"status": "healthy", ...}`

### Using curl (Command Line)
```bash
# Test from another computer/phone terminal
curl http://*************:8000/health

# Test optimized search endpoint
curl -X POST "http://*************:8000/apis/optimized/search/optimized" \
  -H "Content-Type: application/json" \
  -d '{
    "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-02-15"}],
    "ADT": 1, "CHD": 0, "INF": 0,
    "Cabin": "E", "FareType": "REGULAR"
  }'
```

### Using Mobile Browser
1. **Open browser on phone/tablet**
2. **Go to**: `http://*************:8000/docs`
3. **Try the interactive API documentation**

## 🔍 Troubleshooting

### Common Issues

**1. "Connection Refused" Error**
- ✅ Check if server is running: `http://localhost:8000/health`
- ✅ Verify firewall allows port 8000
- ✅ Confirm devices are on same network

**2. "Timeout" Error**
- ✅ Check Windows Firewall settings
- ✅ Verify antivirus isn't blocking connections
- ✅ Try temporarily disabling firewall for testing

**3. "404 Not Found" Error**
- ✅ Verify the correct IP address
- ✅ Check the URL path is correct
- ✅ Ensure server started successfully

**4. Server Won't Start**
- ✅ Check if port 8000 is already in use
- ✅ Verify Python dependencies are installed
- ✅ Check Redis and MySQL are running

### Diagnostic Commands

**Check if port is in use:**
```cmd
netstat -an | findstr :8000
```

**Test local connectivity:**
```bash
curl http://localhost:8000/health
```

**Check firewall status:**
```powershell
Get-NetFirewallRule -DisplayName "*Python*"
```

## 📊 Performance Monitoring

### Real-time Stats
Visit: `http://YOUR_LOCAL_IP:8000/apis/optimized/search/performance/stats`

### Performance Testing
```bash
# Run the performance test suite
python test_performance_optimization.py
```

### Key Metrics to Monitor
- **Response Time**: Should be ≤3 seconds
- **Cache Hit Rate**: Target >85%
- **Error Rate**: Should be <1%
- **Memory Usage**: Monitor cache size

## 🔒 Security Considerations

### For Development/Testing
- ✅ Use only on trusted local networks
- ✅ Don't expose to public internet
- ✅ Keep firewall rules specific to port 8000

### For Production
- ❌ **DO NOT** use `0.0.0.0` host in production
- ✅ Use proper SSL certificates
- ✅ Implement authentication
- ✅ Use reverse proxy (nginx/Apache)

## 📱 Mobile App Integration

### Frontend Configuration
Update your mobile app's API base URL to:
```javascript
const API_BASE_URL = 'http://*************:8000/apis';
```

### CORS Configuration
The server is configured to allow cross-origin requests. Update the `ORIGIN` environment variable if needed:
```env
ORIGIN=http://localhost:3000,http://*************:3000,http://YOUR_MOBILE_APP_URL
```

## 🎯 Next Steps

1. **Test Basic Connectivity**: Verify health endpoint works from another device
2. **Test API Endpoints**: Try the optimized search endpoints
3. **Monitor Performance**: Check response times and cache hit rates
4. **Integrate with Frontend**: Update your mobile app to use the local server
5. **Load Testing**: Use the provided test scripts to validate performance

## 📞 Support

If you encounter issues:

1. **Check the server logs** for error messages
2. **Verify network connectivity** between devices
3. **Test with firewall temporarily disabled**
4. **Use the diagnostic commands** provided above
5. **Check the performance stats** endpoint for system health

---

**Happy coding! 🚀**
