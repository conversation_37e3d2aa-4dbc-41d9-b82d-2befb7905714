import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Box,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Speed as PerformanceIcon,
  Storage as CacheIcon,
  Search as SearchIcon,
  Settings as SettingsIcon,
  Assessment as AnalyticsIcon,
  BugReport as ErrorIcon,
  CloudQueue as TripJackIcon,
  People as UsersIcon,
  Notifications as AlertsIcon,
  GetApp as ExportIcon,
  HealthAndSafety as HealthIcon,
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppContext } from '../../contexts/AppContext';

const DRAWER_WIDTH = 280;

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactElement;
  path: string;
  badge?: number;
  color?: string;
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

const Sidebar: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { state, setSidebarOpen } = useAppContext();

  const getColorFromPalette = (color: string) => {
    switch (color) {
      case 'success':
        return theme.palette.success.main;
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const getChipColor = (color?: string): 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' => {
    switch (color) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      case 'secondary':
        return 'secondary';
      default:
        return 'primary';
    }
  };

  const menuSections: MenuSection[] = [
    {
      title: 'Overview',
      items: [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: <DashboardIcon />,
          path: '/',
        },
        {
          id: 'health',
          label: 'System Health',
          icon: <HealthIcon />,
          path: '/health',
          color: state.systemHealth.api === 'healthy' ? 'success' : 'error',
        },
      ],
    },
    {
      title: 'Monitoring',
      items: [
        {
          id: 'performance',
          label: 'Performance',
          icon: <PerformanceIcon />,
          path: '/performance',
        },
        {
          id: 'cache',
          label: 'Cache Management',
          icon: <CacheIcon />,
          path: '/cache',
        },
        {
          id: 'searches',
          label: 'Flight Searches',
          icon: <SearchIcon />,
          path: '/searches',
          badge: state.activeSearches.length,
        },
        {
          id: 'tripjack',
          label: 'TripJack Integration',
          icon: <TripJackIcon />,
          path: '/tripjack',
          color: state.systemHealth.tripjack === 'available' ? 'success' : 'warning',
        },
      ],
    },
    {
      title: 'Analytics',
      items: [
        {
          id: 'analytics',
          label: 'Route Analytics',
          icon: <AnalyticsIcon />,
          path: '/analytics',
        },
        {
          id: 'errors',
          label: 'Error Logs',
          icon: <ErrorIcon />,
          path: '/errors',
        },
        {
          id: 'alerts',
          label: 'System Alerts',
          icon: <AlertsIcon />,
          path: '/alerts',
          badge: state.alerts.filter(alert => !alert.acknowledged).length,
          color: state.alerts.some(alert => alert.severity === 'critical') ? 'error' : 'warning',
        },
      ],
    },
    {
      title: 'Administration',
      items: [
        {
          id: 'settings',
          label: 'Configuration',
          icon: <SettingsIcon />,
          path: '/settings',
        },
        {
          id: 'users',
          label: 'User Management',
          icon: <UsersIcon />,
          path: '/users',
        },
        {
          id: 'export',
          label: 'Data Export',
          icon: <ExportIcon />,
          path: '/export',
        },
      ],
    },
  ];

  const handleItemClick = (path: string) => {
    navigate(path);
    if (window.innerWidth < theme.breakpoints.values.md) {
      setSidebarOpen(false);
    }
  };

  const isSelected = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const renderMenuItem = (item: MenuItem) => (
    <ListItem key={item.id} disablePadding>
      <ListItemButton
        selected={isSelected(item.path)}
        onClick={() => handleItemClick(item.path)}
        sx={{
          borderRadius: 1,
          mx: 1,
          mb: 0.5,
          '&.Mui-selected': {
            backgroundColor: alpha(theme.palette.primary.main, 0.12),
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.16),
            },
          },
          '&:hover': {
            backgroundColor: alpha(theme.palette.action.hover, 0.08),
          },
        }}
      >
        <ListItemIcon
          sx={{
            color: isSelected(item.path)
              ? theme.palette.primary.main
              : theme.palette.text.secondary,
            minWidth: 40,
          }}
        >
          {item.icon}
        </ListItemIcon>
        <ListItemText
          primary={item.label}
          sx={{
            '& .MuiListItemText-primary': {
              fontSize: '0.875rem',
              fontWeight: isSelected(item.path) ? 600 : 400,
              color: isSelected(item.path)
                ? theme.palette.primary.main
                : theme.palette.text.primary,
            },
          }}
        />
        {item.badge !== undefined && item.badge > 0 && (
          <Chip
            label={item.badge}
            size="small"
            color={getChipColor(item.color)}
            sx={{
              height: 20,
              fontSize: '0.75rem',
              fontWeight: 600,
            }}
          />
        )}
        {item.color && item.badge === undefined && (
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: getColorFromPalette(item.color),
            }}
          />
        )}
      </ListItemButton>
    </ListItem>
  );

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: `1px solid ${theme.palette.divider}`,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          color: 'white',
        }}
      >
        <Typography variant="h6" fontWeight={700}>
          Fast Travel
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          Admin Dashboard
        </Typography>
      </Box>

      {/* Connection Status */}
      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: state.wsConnected
                ? theme.palette.success.main
                : theme.palette.error.main,
            }}
          />
          <Typography variant="caption" color="text.secondary">
            {state.wsConnected ? 'Real-time Connected' : 'Offline Mode'}
          </Typography>
        </Box>
      </Box>

      {/* Menu Sections */}
      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>
        {menuSections.map((section, index) => (
          <Box key={section.title}>
            <Typography
              variant="overline"
              sx={{
                px: 2,
                py: 1,
                display: 'block',
                color: theme.palette.text.secondary,
                fontSize: '0.75rem',
                fontWeight: 600,
                letterSpacing: 1,
              }}
            >
              {section.title}
            </Typography>
            <List dense sx={{ px: 0 }}>
              {section.items.map(renderMenuItem)}
            </List>
            {index < menuSections.length - 1 && (
              <Divider sx={{ mx: 2, my: 1 }} />
            )}
          </Box>
        ))}
      </Box>

      {/* Footer */}
      <Box
        sx={{
          p: 2,
          borderTop: `1px solid ${theme.palette.divider}`,
          backgroundColor: alpha(theme.palette.background.paper, 0.8),
        }}
      >
        <Typography variant="caption" color="text.secondary" display="block">
          Version 1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          Last updated: {new Date().toLocaleDateString()}
        </Typography>
      </Box>
    </Box>
  );

  return (
    <>
      {/* Desktop Drawer */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={state.sidebarOpen}
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            width: DRAWER_WIDTH,
            boxSizing: 'border-box',
            borderRight: `1px solid ${theme.palette.divider}`,
            backgroundColor: theme.palette.background.paper,
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="left"
        open={state.sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            width: DRAWER_WIDTH,
            boxSizing: 'border-box',
          },
        }}
      >
        {drawerContent}
      </Drawer>
    </>
  );
};

export default Sidebar;
