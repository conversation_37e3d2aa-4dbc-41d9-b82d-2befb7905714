from fastapi import FastAPI
from app.api_router import api_router
from app.microservices.payment_service.routes import router as payment_service_router
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from app.microservices.auth_service.config import database as auth_service_database
from app.microservices.flight_service.config import database as flight_service_database
from app.microservices.booking_service.config import database as booking_service_database

load_dotenv()
env = os.environ

# Initialize FastAPI app
app = FastAPI()

app.include_router(api_router, prefix="/apis")
# Include payment router separately to handle different URL patterns
app.include_router(payment_service_router, tags=["Payment"])

app.debug = False

# Define origins that are allowed to access the API
origins = env.get('ORIGIN').split(',')

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,           # List of allowed origins
    allow_credentials=True,          # Allow sending cookies from the frontend
    allow_methods=["*"],             # Allow all HTTP methods
    allow_headers=["*"],             # Allow all headers
)

# Connect to the database at startup
@app.on_event("startup")
async def startup():
    await auth_service_database.connect()
    await flight_service_database.connect()
    await booking_service_database.connect()

# Disconnect from the database on shutdown
@app.on_event("shutdown")
async def shutdown():
    await flight_service_database.disconnect()
    await auth_service_database.disconnect()
    await booking_service_database.disconnect()

