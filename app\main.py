from fastapi import FastAPI
from fastapi.responses import JSONResponse
from app.api_router import api_router
from app.microservices.payment_service.routes import router as payment_service_router
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
import time
import socket
from datetime import datetime
from app.microservices.auth_service.config import database as auth_service_database
from app.microservices.flight_service.config import database as flight_service_database
from app.microservices.booking_service.config import database as booking_service_database

load_dotenv()
env = os.environ

# Initialize FastAPI app with enhanced metadata for local network access
app = FastAPI(
    title="Fast Travel Backend API",
    description="Flight Booking API with Performance Optimizations",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Health check and network info endpoints
@app.get("/health")
async def health_check():
    """Simple health check endpoint for network connectivity testing."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "server": "Fast Travel Backend",
        "version": "1.0.0",
        "network_accessible": True
    }

@app.get("/network-info")
async def network_info():
    """Get network access information for local network setup."""
    try:
        # Get local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
    except Exception:
        local_ip = "127.0.0.1"

    return {
        "local_ip": local_ip,
        "endpoints": {
            "health": f"http://{local_ip}:8000/health",
            "docs": f"http://{local_ip}:8000/docs",
            "flight_search": f"http://{local_ip}:8000/apis/search",
            "optimized_search": f"http://{local_ip}:8000/apis/optimized/search/optimized",
            "performance_stats": f"http://{local_ip}:8000/apis/optimized/search/performance/stats"
        },
        "access_info": {
            "local_access": ["http://localhost:8000", "http://127.0.0.1:8000"],
            "network_access": f"http://{local_ip}:8000",
            "firewall_note": "Ensure port 8000 is open in firewall for network access"
        }
    }

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Fast Travel Backend API",
        "status": "running",
        "docs": "/docs",
        "health": "/health",
        "network_info": "/network-info",
        "optimized_endpoints": {
            "search": "/apis/optimized/search/optimized",
            "stats": "/apis/optimized/search/performance/stats"
        }
    }

app.include_router(api_router, prefix="/apis")
# Include payment router separately to handle different URL patterns
app.include_router(payment_service_router, tags=["Payment"])

app.debug = False

# Define origins that are allowed to access the API
origins = env.get('ORIGIN').split(',')

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,           # List of allowed origins
    allow_credentials=True,          # Allow sending cookies from the frontend
    allow_methods=["*"],             # Allow all HTTP methods
    allow_headers=["*"],             # Allow all headers
)

# Connect to the database at startup
@app.on_event("startup")
async def startup():
    await auth_service_database.connect()
    await flight_service_database.connect()
    await booking_service_database.connect()

# Disconnect from the database on shutdown
@app.on_event("shutdown")
async def shutdown():
    await flight_service_database.disconnect()
    await auth_service_database.disconnect()
    await booking_service_database.disconnect()

