"""
Database optimization and performance management routes.
Provides endpoints for database monitoring, optimization, and analytics.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
from pydantic import BaseModel

from app.microservices.flight_service.database.connection_pool import db_connection_pool
from app.microservices.booking_service.optimized_services import optimized_booking_service
from app.microservices.flight_service.analytics.performance_service import performance_analytics


# Pydantic models for request validation
class BookingCreationRequest(BaseModel):
    booking_data: Dict[str, Any]
    flight_details: Dict[str, Any]
    travellers: list
    contact_info: Dict[str, Any]


class BookingRetrievalRequest(BaseModel):
    booking_id: str
    user_id: Optional[str] = None


class UserBookingsRequest(BaseModel):
    user_id: str
    limit: int = 20
    offset: int = 0


class DatabaseQueryRequest(BaseModel):
    query: str
    params: Optional[Dict[str, Any]] = None
    fetch_all: bool = True


class PerformanceReportRequest(BaseModel):
    hours: int = 24


# Create router instance
router = APIRouter()


@router.get("/database/stats")
async def get_database_statistics():
    """
    Get comprehensive database performance statistics.
    
    Returns:
        Database performance metrics and connection pool stats
    """
    try:
        stats = db_connection_pool.get_performance_stats()
        return {
            "status": "success",
            "database_stats": stats,
            "timestamp": performance_analytics.analytics_stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve database stats: {str(e)}")


@router.post("/database/query")
async def execute_database_query(request: DatabaseQueryRequest):
    """
    Execute optimized database query with performance tracking.
    
    Args:
        request: Database query request
        
    Returns:
        Query results with performance metadata
    """
    try:
        import time
        start_time = time.time()
        
        # Execute query
        result = await db_connection_pool.execute_async_query(
            request.query,
            request.params,
            request.fetch_all
        )
        
        execution_time = time.time() - start_time
        
        # Record performance metric
        await performance_analytics.record_metric(
            "database_query_time",
            execution_time * 1000,  # Convert to milliseconds
            "database",
            {"query_type": db_connection_pool._get_query_type(request.query)}
        )
        
        return {
            "status": "success",
            "result": result,
            "execution_time_ms": round(execution_time * 1000, 2),
            "query_type": db_connection_pool._get_query_type(request.query)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database query failed: {str(e)}")


@router.post("/database/cleanup-cache")
async def cleanup_database_cache():
    """
    Clean up expired database query cache entries.
    
    Returns:
        Cleanup results
    """
    try:
        cleaned_count = await db_connection_pool.cleanup_expired_cache()
        
        return {
            "status": "success",
            "message": f"Cleaned up {cleaned_count} expired cache entries",
            "cleaned_entries": cleaned_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database cache cleanup failed: {str(e)}")


@router.post("/booking/create-optimized")
async def create_booking_optimized(request: BookingCreationRequest):
    """
    Create booking with optimized database operations.
    
    Args:
        request: Booking creation request
        
    Returns:
        Created booking information with performance metadata
    """
    try:
        result = await optimized_booking_service.create_booking_optimized(
            request.booking_data,
            request.flight_details,
            request.travellers,
            request.contact_info
        )
        
        # Record performance metric
        await performance_analytics.record_metric(
            "booking_creation_time",
            result.get("creation_time_ms", 0),
            "booking_service",
            {"database_operations": result.get("database_operations", 0)}
        )
        
        return {
            "status": "success",
            "booking": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Optimized booking creation failed: {str(e)}")


@router.post("/booking/get-optimized")
async def get_booking_optimized(request: BookingRetrievalRequest):
    """
    Retrieve booking with optimized caching and queries.
    
    Args:
        request: Booking retrieval request
        
    Returns:
        Booking information with performance metadata
    """
    try:
        result = await optimized_booking_service.get_booking_optimized(
            request.booking_id,
            request.user_id
        )
        
        if result:
            # Record performance metric
            await performance_analytics.record_metric(
                "booking_retrieval_time",
                result.get("retrieval_time_ms", 0),
                "booking_service",
                {
                    "cache_hit": result.get("cache_hit", False),
                    "database_queries": result.get("database_queries", 0)
                }
            )
            
            return {
                "status": "success",
                "booking": result
            }
        else:
            return {
                "status": "not_found",
                "message": "Booking not found"
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Optimized booking retrieval failed: {str(e)}")


@router.post("/booking/user-bookings-optimized")
async def get_user_bookings_optimized(request: UserBookingsRequest):
    """
    Get user bookings with optimized pagination and caching.
    
    Args:
        request: User bookings request
        
    Returns:
        Paginated booking list with performance metadata
    """
    try:
        result = await optimized_booking_service.get_user_bookings_optimized(
            request.user_id,
            request.limit,
            request.offset
        )
        
        # Record performance metric
        await performance_analytics.record_metric(
            "user_bookings_retrieval_time",
            result.get("retrieval_time_ms", 0),
            "booking_service",
            {
                "bookings_count": len(result.get("bookings", [])),
                "database_queries": result.get("database_queries", 0)
            }
        )
        
        return {
            "status": "success",
            "result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Optimized user bookings retrieval failed: {str(e)}")


@router.get("/booking/service-stats")
async def get_booking_service_statistics():
    """
    Get booking service performance statistics.
    
    Returns:
        Booking service performance metrics
    """
    try:
        stats = optimized_booking_service.get_service_stats()
        return {
            "status": "success",
            "booking_service_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve booking service stats: {str(e)}")


@router.get("/analytics/dashboard")
async def get_performance_dashboard():
    """
    Get comprehensive performance analytics dashboard.
    
    Returns:
        Performance dashboard with metrics, trends, and alerts
    """
    try:
        dashboard = await performance_analytics.get_performance_dashboard()
        return {
            "status": "success",
            "dashboard": dashboard
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve performance dashboard: {str(e)}")


@router.post("/analytics/report")
async def generate_performance_report(request: PerformanceReportRequest):
    """
    Generate comprehensive performance report.
    
    Args:
        request: Performance report request
        
    Returns:
        Detailed performance report
    """
    try:
        report = await performance_analytics.generate_performance_report(request.hours)
        return {
            "status": "success",
            "report": report
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate performance report: {str(e)}")


@router.get("/analytics/stats")
async def get_analytics_statistics():
    """
    Get analytics service statistics.
    
    Returns:
        Analytics service performance metrics
    """
    try:
        stats = performance_analytics.get_analytics_stats()
        return {
            "status": "success",
            "analytics_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve analytics stats: {str(e)}")


@router.post("/analytics/record-metric")
async def record_custom_metric(
    metric_type: str,
    value: float,
    service: str,
    metadata: Optional[Dict[str, Any]] = None
):
    """
    Record a custom performance metric.
    
    Args:
        metric_type: Type of metric
        value: Metric value
        service: Service name
        metadata: Additional metadata
        
    Returns:
        Confirmation of metric recording
    """
    try:
        await performance_analytics.record_metric(metric_type, value, service, metadata)
        
        return {
            "status": "success",
            "message": f"Metric {metric_type} recorded for service {service}",
            "metric_type": metric_type,
            "value": value,
            "service": service
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to record metric: {str(e)}")


@router.get("/database/connection-pool/health")
async def check_database_pool_health():
    """
    Check database connection pool health.
    
    Returns:
        Database pool health status
    """
    try:
        stats = db_connection_pool.get_performance_stats()
        pool_stats = stats.get("pool_stats", {})
        
        # Determine health status
        active_connections = pool_stats.get("active_connections", 0)
        failed_queries = pool_stats.get("failed_queries", 0)
        total_queries = pool_stats.get("total_queries", 1)
        
        failure_rate = failed_queries / total_queries
        
        if failure_rate > 0.1:  # More than 10% failure rate
            health_status = "critical"
        elif failure_rate > 0.05:  # More than 5% failure rate
            health_status = "warning"
        else:
            health_status = "healthy"
        
        return {
            "status": "success",
            "health_status": health_status,
            "active_connections": active_connections,
            "failure_rate": failure_rate,
            "pool_stats": pool_stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database health check failed: {str(e)}")


@router.post("/database/initialize-pools")
async def initialize_database_pools():
    """
    Initialize database connection pools.
    
    Returns:
        Initialization status
    """
    try:
        # Initialize async pool
        await db_connection_pool.initialize_async_pool()
        
        # Initialize sync pool
        db_connection_pool.initialize_sync_pool()
        
        return {
            "status": "success",
            "message": "Database connection pools initialized successfully",
            "async_pool": "initialized",
            "sync_pool": "initialized"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database pool initialization failed: {str(e)}")


@router.post("/database/close-pools")
async def close_database_pools():
    """
    Close database connection pools.
    
    Returns:
        Closure status
    """
    try:
        # Close async pool
        await db_connection_pool.close_async_pool()
        
        # Close sync pool
        db_connection_pool.close_sync_pool()
        
        return {
            "status": "success",
            "message": "Database connection pools closed successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database pool closure failed: {str(e)}")


@router.get("/performance/system-health")
async def get_system_health():
    """
    Get comprehensive system health status.
    
    Returns:
        System health summary with component status
    """
    try:
        dashboard = await performance_analytics.get_performance_dashboard()
        system_health = dashboard.get("system_health", {})
        
        return {
            "status": "success",
            "system_health": system_health,
            "overall_status": system_health.get("overall_status", "unknown"),
            "overall_score": system_health.get("overall_score", 0.0),
            "component_health": system_health.get("component_health", {})
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"System health check failed: {str(e)}")
