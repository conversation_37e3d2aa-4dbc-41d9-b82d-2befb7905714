import requests
import json
from app.microservices.flight_service import config


def make_provider_api_call(provider_url: str, provider_request: dict,) -> dict:
    """
    Makes an API call to the provider with the given request payload and API key in the header.

    :param provider_url: The URL of the provider API endpoint.
    :param provider_request: The request payload formatted for the provider.
    :param api_key: The API key for authenticating the request.
    :return: The response from the provider as a dictionary.
    :raises SuspiciousOperation: If the provider API call fails or returns an error.
    """
    # TripJack API headers - ensure proper format
    api_key = config.PROVIDER_CONFIG.get('tripjack', {}).get('api_key', '')
    headers = {
        "apikey": api_key,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # Debug logging
    print(f"TripJack API Call Debug:")
    print(f"URL: {provider_url}")
    print(f"Headers: {headers}")
    print(f"Request Payload: {json.dumps(provider_request, indent=2)}")

    try:
        response = requests.post(provider_url, json=provider_request, headers=headers)

        # Log response details for debugging
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")

        if response.status_code != 200:
            print(f"Response Body: {response.text}")

        response.raise_for_status()  # Raise an error if the request was not successful
        return response.json()  # Return the response as a dictionary
    except requests.RequestException as e:
        # Enhanced error logging
        error_details = f"Provider API call failed: {str(e)}"
        if hasattr(e, 'response') and e.response is not None:
            error_details += f" | Status: {e.response.status_code} | Response: {e.response.text}"
        print(f"TripJack API Error: {error_details}")
        raise RuntimeError(error_details) from e
